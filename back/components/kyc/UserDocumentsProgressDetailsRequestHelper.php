<?php

declare(strict_types=1);

namespace app\back\components\kyc;

use app\back\components\BaseAuthAccess;
use app\back\components\helpers\Json;
use app\back\components\Initializable;
use app\back\components\MaskGenerator;
use app\back\entities\CanonicalPaySySource;
use app\back\entities\UserDocumentProgress;
use app\back\entities\UserKyc;
use app\back\repositories\CanonicalPaySySources;
use app\back\repositories\CanonicalPaySystems;
use app\back\repositories\PaySystems;
use app\back\repositories\Requisites;
use app\back\repositories\S2pOrders;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Query\Query;

trait UserDocumentsProgressDetailsRequestHelper
{
    private MaskGenerator $maskGenerator;
    private BaseAuthAccess $authAccess;

    public const array DOC_TYPES_REQUISITE_DEPENDENT = [
        ...self::DOC_TYPES_REQUISITE_DEPENDENT_WITHOUT_OTHER,
        UserDocumentProgress::TYPE_OTHER,
    ];
    public const array DOC_TYPES_REQUISITE_DEPENDENT_WITHOUT_OTHER = [
        UserDocumentProgress::TYPE_SELFIE_WITH_BANK_CARD,
        UserDocumentProgress::TYPE_BANK_STATEMENT,
        UserDocumentProgress::TYPE_BANK_SCREENSHOT,
        UserDocumentProgress::TYPE_BANK_CARD_FRONT,
        UserDocumentProgress::TYPE_BANK_CARD_BACK,
        UserDocumentProgress::TYPE_MOBILE_COMMERCE_CONTRACT,
        UserDocumentProgress::TYPE_WALLET_ACCOUNT_SCREENSHOT,
    ];
    public const array DOC_TYPES_DEFAULT = [
        UserDocumentProgress::TYPE_PASSPORT_OR_ID_CARD,
        UserDocumentProgress::TYPE_SELFIE_WITH_PASSPORT_OR_ID_CARD
    ];

    #[Initializable]
    final public function initMaskGenAndAuthAccess(MaskGenerator $maskGenerator, BaseAuthAccess $authAccess): void
    {
        $this->maskGenerator = $maskGenerator;
        $this->authAccess = $authAccess;
    }

    public function requestDetailsJoinQuery(string $progressTableAlias): Query
    {
        $progressTableAlias = $this->db->getQuoter()->quoteTableName($progressTableAlias);
        return $this->usJoinCpsAndRequisite(
            (new Query($this->db))
                ->select([
                    'request_details' => "jsonb_agg(d.details || coalesce(jsonb_build_object(
                        'canonicalPaySystem', cps.name,
                        'requisite', coalesce(o.requisite, r.requisite, us.wallet)
                    ), '{}'::jsonb))",
                ])
                ->from(['d' => (new Query($this->db))->select(['details' => "jsonb_array_elements($progressTableAlias.details)"])])
                ->leftJoin(['us' => UserTransactions::TABLE_NAME], [
                    'AND',
                    "us.site_id = $progressTableAlias.site_id",
                    "us.user_id = $progressTableAlias.user_id",
                    "us.transaction_id = d.details->>'" . UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID . "'"
                ])
                ->where([
                    'AND',
                    ["$progressTableAlias.action" => UserDocumentProgress::ACTION_STATUS_CHANGE],
                    ["$progressTableAlias.kyc_status" => [UserKyc::KYC_REQUEST, UserKyc::KYC_REQUEST_REPEAT]],
                ])
        );
    }

    public function maskedTransactionsDetails(int $siteId, int $userId, array $transactionId): array
    {
        $transactionInfoQuery = $this->usJoinCpsAndRequisite(
            (new Query($this->db))
                ->select([
                    'transactions_data' => "jsonb_agg(jsonb_build_object(
                        '" . UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID . "', us.transaction_id,
                        'canonicalPaySystem', cps.name,
                        'requisite', coalesce(o.requisite, r.requisite, us.wallet)
                    ))",
                ])
                ->from(['us' => UserTransactions::TABLE_NAME])
                ->where([
                    'us.site_id' => $siteId,
                    'us.user_id' => $userId,
                    'us.transaction_id' => $transactionId,
                ])
        );

        return array_map(
            $this->transactionStr(...),
            Json::decode($transactionInfoQuery->scalar() ?: '[]')
        );
    }

    private function usJoinCpsAndRequisite(Query $usQuery): Query
    {
        return $usQuery
            ->leftJoin(['o' => S2pOrders::TABLE_NAME], 'o.site_id = us.site_id AND o.user_id = us.user_id AND o.invoice_id = us.transaction_id')
            ->leftJoin(['r' => Requisites::TABLE_NAME], 'r.id = us.requisite_id')
            ->leftJoin(['ps' => PaySystems::TABLE_NAME], 'ps.id = us.pay_sys_id')
            ->leftJoin(['pss' => CanonicalPaySySources::TABLE_NAME], [
                'AND',
                'pss.name = ps.name',
                ['pss.source' => CanonicalPaySySource::SOURCE_PRODUCT],
            ])
            ->leftJoin(['cps' => CanonicalPaySystems::TABLE_NAME], 'cps.id = pss.canonical_pay_sys_id');
    }

    protected function detailsDecorateDocsRequest(array $details): array
    {
        $rows = array_map(fn($d) => array_filter([
            $this->reasonsStr($d),
            $this->commentStr($d),
            $this->transactionStr($d),
            $d[UserDocumentProgress::DOC_DETAIL_FIELD_REQUISITE_HINT] ?? null,
            $this->requestDocTypeStr($d),
        ]), $details);

        $uniqueReasons = array_values(array_unique(array_column($rows, 0)));

        if (count($uniqueReasons) === 1 && count($rows) > 1) {
            return ["$uniqueReasons[0]:", ...array_map(static fn($info) => implode(' ', array_reverse(array_slice($info, 1))), $rows)];
        }

        return array_map(static fn($info) => array_pop($info) . ": " . implode(' ', array_reverse($info)), $rows);
    }

    private function reasonsStr(array $details): string
    {
        return implode(', ', array_map(
            static fn($r) => UserDocumentProgress::REASONS[$r] ?? $r ?? '-',
            $details[UserDocumentProgress::DOC_DETAIL_FIELD_REASONS] ?? []
        ));
    }

    private function commentStr(array $details): ?string
    {
        if (empty($details[UserDocumentProgress::DOC_DETAIL_FIELD_COMMENT])) {
            return null;
        }

        return $details[UserDocumentProgress::DOC_DETAIL_FIELD_COMMENT];
    }

    private function requestDocTypeStr(array $details)
    {
        return UserDocumentProgress::TYPES[$details[UserDocumentProgress::DOC_DETAIL_FIELD_TYPE] ?? null]
            ?? $details[UserDocumentProgress::DOC_DETAIL_FIELD_TYPE]
            ?? '[empty type]';
    }

    private function transactionStr(array $row): ?string
    {
        if (!isset($row[UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID])) {
            return null;
        }

        return implode(' ', array_filter([
            "Tr. id: {$row[UserDocumentProgress::DOC_DETAIL_FIELD_TRANSACTION_ID]}",
            $row['canonicalPaySystem'],
            $this->authAccess->canViewRequisites() ? $row['requisite'] : $this->maskGenerator->mask($row['requisite']),
        ]));
    }
}
