<?php

declare(strict_types=1);

namespace app\back\components\helpers;

/** Regexps from Yii2 StrHelper and Inflector */
class Str
{
    private const array PLURALS = [
        '/([nrlm]ese|deer|fish|sheep|measles|ois|pox|media)$/i' => '\1',
        '/^(sea[- ]bass)$/i' => '\1',
        '/(m)ove$/i' => '\1oves',
        '/(f)oot$/i' => '\1eet',
        '/(h)uman$/i' => '\1umans',
        '/(s)tatus$/i' => '\1tatuses',
        '/(s)taff$/i' => '\1taff',
        '/(t)ooth$/i' => '\1eeth',
        '/(quiz)$/i' => '\1zes',
        '/^(ox)$/i' => '\1\2en',
        '/([m|l])ouse$/i' => '\1ice',
        '/(matr|vert|ind)(ix|ex)$/i' => '\1ices',
        '/(x|ch|ss|sh)$/i' => '\1es',
        '/([^aeiouy]|qu)y$/i' => '\1ies',
        '/(hive)$/i' => '\1s',
        '/(?:([^f])fe|([lr])f)$/i' => '\1\2ves',
        '/sis$/i' => 'ses',
        '/([ti])um$/i' => '\1a',
        '/(p)erson$/i' => '\1eople',
        '/(m)an$/i' => '\1en',
        '/(c)hild$/i' => '\1hildren',
        '/(buffal|tomat|potat|ech|her|vet)o$/i' => '\1oes',
        '/(alumn|bacill|cact|foc|fung|nucle|radi|stimul|syllab|termin|vir)us$/i' => '\1i',
        '/us$/i' => 'uses',
        '/(alias)$/i' => '\1es',
        '/(ax|cris|test)is$/i' => '\1es',
        '/(currenc)y$/' => '\1ies',
        '/s$/' => 's',
        '/^$/' => '',
        '/$/' => 's',
    ];

    private const array SINGULARS = [
        '/([nrlm]ese|deer|fish|sheep|measles|ois|pox|media|ss)$/i' => '\1',
        '/^(sea[- ]bass)$/i' => '\1',
        '/(s)tatuses$/i' => '\1tatus',
        '/(f)eet$/i' => '\1oot',
        '/(t)eeth$/i' => '\1ooth',
        '/^(.*)(menu)s$/i' => '\1\2',
        '/(quiz)zes$/i' => '\\1',
        '/(matr)ices$/i' => '\1ix',
        '/(vert|ind)ices$/i' => '\1ex',
        '/^(ox)en/i' => '\1',
        '/(alias)(es)*$/i' => '\1',
        '/(alumn|bacill|cact|foc|fung|nucle|radi|stimul|syllab|termin|viri?)i$/i' => '\1us',
        '/([ftw]ax)es/i' => '\1',
        '/(cris|ax|test)es$/i' => '\1is',
        '/(shoe|slave)s$/i' => '\1',
        '/(o)es$/i' => '\1',
        '/ouses$/' => 'ouse',
        '/([^a])uses$/' => '\1us',
        '/([m|l])ice$/i' => '\1ouse',
        '/(x|ch|ss|sh)es$/i' => '\1',
        '/(m)ovies$/i' => '\1\2ovie',
        '/(s)eries$/i' => '\1\2eries',
        '/([^aeiouy]|qu)ies$/i' => '\1y',
        '/([lr])ves$/i' => '\1f',
        '/(tive)s$/i' => '\1',
        '/(hive)s$/i' => '\1',
        '/(drive)s$/i' => '\1',
        '/([^fo])ves$/i' => '\1fe',
        '/(^analy)ses$/i' => '\1sis',
        '/(analy|diagno|^ba|(p)arenthe|(p)rogno|(s)ynop|(t)he)ses$/i' => '\1\2sis',
        '/([ti])a$/i' => '\1um',
        '/(p)eople$/i' => '\1\2erson',
        '/(m)en$/i' => '\1an',
        '/(c)hildren$/i' => '\1\2hild',
        '/(n)ews$/i' => '\1\2ews',
        '/(n)etherlands$/i' => '\1\2etherlands',
        '/eaus$/' => 'eau',
        '/(currenc)ies$/' => '\1y',
        '/^(.*us)$/' => '\\1',
        '/s$/i' => '',
    ];

    private const array SPECIALS = [
        'atlas' => 'atlases',
        'beef' => 'beefs',
        'brother' => 'brothers',
        'cafe' => 'cafes',
        'child' => 'children',
        'cookie' => 'cookies',
        'corpus' => 'corpuses',
        'cow' => 'cows',
        'curve' => 'curves',
        'foe' => 'foes',
        'ganglion' => 'ganglions',
        'genie' => 'genies',
        'genus' => 'genera',
        'graffito' => 'graffiti',
        'hoof' => 'hoofs',
        'loaf' => 'loaves',
        'man' => 'men',
        'money' => 'monies',
        'mongoose' => 'mongooses',
        'move' => 'moves',
        'mythos' => 'mythoi',
        'niche' => 'niches',
        'numen' => 'numina',
        'occiput' => 'occiputs',
        'octopus' => 'octopuses',
        'opus' => 'opuses',
        'ox' => 'oxen',
        'pasta' => 'pasta',
        'penis' => 'penises',
        'sex' => 'sexes',
        'soliloquy' => 'soliloquies',
        'testis' => 'testes',
        'trilby' => 'trilbys',
        'turf' => 'turfs',
        'wave' => 'waves',
        'Amoyese' => 'Amoyese',
        'bison' => 'bison',
        'Borghese' => 'Borghese',
        'bream' => 'bream',
        'breeches' => 'breeches',
        'britches' => 'britches',
        'buffalo' => 'buffalo',
        'cantus' => 'cantus',
        'carp' => 'carp',
        'chassis' => 'chassis',
        'clippers' => 'clippers',
        'cod' => 'cod',
        'coitus' => 'coitus',
        'Congoese' => 'Congoese',
        'contretemps' => 'contretemps',
        'corps' => 'corps',
        'debris' => 'debris',
        'diabetes' => 'diabetes',
        'djinn' => 'djinn',
        'eland' => 'eland',
        'elk' => 'elk',
        'equipment' => 'equipment',
        'Faroese' => 'Faroese',
        'flounder' => 'flounder',
        'Foochowese' => 'Foochowese',
        'gallows' => 'gallows',
        'Genevese' => 'Genevese',
        'Genoese' => 'Genoese',
        'Gilbertese' => 'Gilbertese',
        'graffiti' => 'graffiti',
        'headquarters' => 'headquarters',
        'herpes' => 'herpes',
        'hijinks' => 'hijinks',
        'Hottentotese' => 'Hottentotese',
        'information' => 'information',
        'innings' => 'innings',
        'jackanapes' => 'jackanapes',
        'Kiplingese' => 'Kiplingese',
        'Kongoese' => 'Kongoese',
        'Lucchese' => 'Lucchese',
        'mackerel' => 'mackerel',
        'Maltese' => 'Maltese',
        'mews' => 'mews',
        'moose' => 'moose',
        'mumps' => 'mumps',
        'Nankingese' => 'Nankingese',
        'news' => 'news',
        'nexus' => 'nexus',
        'Niasese' => 'Niasese',
        'Pekingese' => 'Pekingese',
        'Piedmontese' => 'Piedmontese',
        'pincers' => 'pincers',
        'Pistoiese' => 'Pistoiese',
        'pliers' => 'pliers',
        'Portuguese' => 'Portuguese',
        'proceedings' => 'proceedings',
        'rabies' => 'rabies',
        'rice' => 'rice',
        'rhinoceros' => 'rhinoceros',
        'salmon' => 'salmon',
        'Sarawakese' => 'Sarawakese',
        'scissors' => 'scissors',
        'series' => 'series',
        'Shavese' => 'Shavese',
        'shears' => 'shears',
        'siemens' => 'siemens',
        'species' => 'species',
        'swine' => 'swine',
        'testes' => 'testes',
        'trousers' => 'trousers',
        'trout' => 'trout',
        'tuna' => 'tuna',
        'Vermontese' => 'Vermontese',
        'Wenchowese' => 'Wenchowese',
        'whiting' => 'whiting',
        'wildebeest' => 'wildebeest',
        'Yengeese' => 'Yengeese',
        'software' => 'software',
        'hardware' => 'hardware',
        'sms' => 'smses',
        'hhs' => 'hhs',
    ];

    /**
     * Converts a word to its plural form.
     * For example, 'apple' will become 'apples', and 'child' will become 'children'.
     */
    public static function pluralize(string $word): string
    {
        if (isset(self::SPECIALS[$word])) {
            return self::SPECIALS[$word];
        }
        foreach (self::PLURALS as $rule => $replacement) {
            if (preg_match($rule, $word)) {
                return preg_replace($rule, $replacement, $word);
            }
        }

        return $word;
    }

    public static function singularize(string $word): string
    {
        $result = array_search($word, self::SPECIALS, true);
        if ($result !== false) {
            return $result;
        }
        foreach (self::SINGULARS as $rule => $replacement) {
            if (preg_match($rule, $word)) {
                return preg_replace($rule, $replacement, $word);
            }
        }

        return $word;
    }

    public static function ucwords(string $str): string
    {
        return mb_convert_case($str, MB_CASE_TITLE_SIMPLE, 'UTF-8');
    }

    public static function ucfirst(string $str): string
    {
        $firstChar = mb_substr($str, 0, 1, 'UTF-8');
        $then = mb_substr($str, 1, mb_strlen($str, 'UTF-8') - 1, 'UTF-8');
        return mb_strtoupper($firstChar, 'UTF-8') . $then;
    }

    /**
     * "who's online" => "WhoSOnline".
     */
    public static function camelize(string $str): string
    {
        return str_replace(' ', '', self::ucwords(preg_replace('/[^\pL\pN]+/u', ' ', $str)));
    }

    public static function classify(string $tableName, bool $pluralizeLast = false): string
    {
        $words = array_map([static::class, 'singularize'], explode('_', $tableName));
        $words = array_map([static::class, 'ucfirst'], $words);

        if ($pluralizeLast) {
            $lastKey = array_key_last($words);
            $words[$lastKey] = self::pluralize($words[$lastKey]);
        }

        return implode('', $words);
    }

    /**
     * 'PostTag' => 'Post Tag'
     * @param bool $ucwords whether to capitalize the first letter in each word
     */
    public static function camel2words(string $str, bool $ucwords = true): string
    {
        // Add a space before any uppercase letter preceded by a lowercase letter (xY => x Y)
        // and any uppercase letter preceded by an uppercase letter and followed by a lowercase letter (XYz => X Yz)
        $label = preg_replace('/(?<=\p{Ll})\p{Lu}|(?<=[\p{L}\d])\p{Lu}(?=\p{Ll})|(\d+)/u', ' \0', $str);

        $label = mb_strtolower(trim(str_replace(['-', '_', '.'], ' ', $label)), 'UTF-8');

        return $ucwords ? self::ucwords($label) : $label;
    }

    /**
     * 'PostTag' will be converted to 'post-tag'.
     * @param string $separator the character used to concatenate the words in the ID
     * @param bool|string $strict whether to insert a separator between two consecutive uppercase chars, defaults to false
     */
    public static function camel2id(string $str, string $separator = '-', bool $strict = false): string
    {
        $orUC = $strict ? '|\p{Lu}' : '';
        $res = mb_strtolower(preg_replace("/(?<=\d|\p{Ll}$orUC)(\p{Lu})/u", "$separator\$1", trim($str)), 'UTF-8');
        if ($separator !== '_') {
            $res = str_replace('_', $separator, $res);
        }
        return $res;
    }

    /**
     * 'post-tag' => 'PostTag'.
     * @param string $id the ID to be converted
     * @param string $separator the character used to separate the words in the ID
     */
    public static function id2camel(string $id, string $separator = '-'): string
    {
        return implode('', array_map(
            static fn($v) => self::ucfirst(mb_strtolower($v, 'UTF-8')),
            explode($separator, $id)
        ));
    }

    /**
     * "CamelCased" => "underscored_word".
     */
    public static function camelToUnderscore(string $str): string
    {
        return mb_strtolower(preg_replace('/(?<=\\pL)(\\p{Lu})/u', '_\\1', $str), 'UTF-8');
    }

    public static function wordsToUnderscore(string $str): string
    {
        $camel = str_replace(' ', '', self::ucwords($str));
        return self::camelToUnderscore($camel);
    }

    public static function camelAndUnderScoreToCamel(string $str): string
    {
        return lcfirst(preg_replace_callback('#(_(\w))#u', static fn ($matches) => mb_strtoupper($matches[2]), $str));
    }

    /**
     * Returns a human-readable string from $str.
     */
    public static function humanize(string $str, string $separatorToReplace = '_'): string
    {
        return self::ucfirst(str_replace($separatorToReplace, ' ', preg_replace('/_id$/', '', $str)));
    }

    public static function cleanString(null|int|string $val): ?string
    {
        if ($val === null) {
            return null;
        }

        $val = str_replace("\000", '', $val); // "<\000script>alert(219);</script\000>" saved to postgresql with escaping, different from PHP

        return trim(mb_convert_encoding($val, 'UTF-8', 'UTF-8'));
    }

    public static function emailToLdap(?string $value): ?string
    {
        return $value ? mb_split('@', $value, 2)[0] : null;
    }

    public static function beautySize($size, $accuracy = 2): string
    {
        foreach (['GB', 'MB', 'KB', 'B'] as $n => $u) {
            $div = 1024 ** (3 - $n);
            if ($size > $div) {
                return number_format($size / $div, $u === 'B' ? 0 : $accuracy) . ' ' . $u;
            }
        }

        return '0';
    }

    public static function floatListInfo($name, array $data, string $delimiter = ' '): string
    {
        $count = count($data);
        if ($count === 0) {
            return "{$name}(0){$delimiter}min:0.000{$delimiter}max:0.000{$delimiter}avg:0.000{$delimiter}sum:0.000";
        }

        $sum = array_sum($data);

        return "{$name}($count){$delimiter}" . implode($delimiter, array_map(static function ($val) {
                return $val[0] . ':' . number_format($val[1], 3);
        }, [
            ['min', min($data)],
            ['max', max($data)],
            ['avg', $sum / $count],
            ['sum', $sum]
        ]));
    }

    public static function pluralizeByCount(int $count, string $word): string
    {
        $word = abs($count) === 1 ? $word : self::pluralize($word);
        return "{$count} {$word}";
    }

    public static function explodeText(null|int|string $value, string $separator = "\n", bool $uniq = false): array
    {
        $values = explode($separator, str_replace("\xc2\xa0", ' ', $value ?? '')); // NBSP => regular space, then => array
        $result = array_filter(array_map('trim', $values), static fn($val) => $val !== '');

        return array_values($uniq ? array_unique($result) : $result);
    }

    public static function pathFromUrl(string $url): string
    {
        $qMarkPos = strpos($url, "?");
        if (false !== $qMarkPos) {
            $url = substr($url, 0, $qMarkPos);
        }
        $hashPos = strpos($url, "#");
        if (false !== $hashPos) {
            $url = substr($url, 0, $hashPos);
        }
        return $url;
    }

    // https://stackoverflow.com/questions/1755144/how-to-validate-domain-name-in-php
    public static function isValidDomain(?string $domain): bool
    {
        // Check for starting and ending hyphen(s)
        if (str_starts_with($domain, '-') || str_ends_with($domain, '-')) {
            return false;
        }

        // Detect and convert international UTF-8 domain names to IDNA ASCII form
        if (mb_detect_encoding($domain) !== "ASCII") {
            $idn_dom = idn_to_ascii($domain);
        } else {
            $idn_dom = $domain;
        }

        // Validate
        return filter_var($idn_dom, FILTER_VALIDATE_DOMAIN, FILTER_FLAG_HOSTNAME) !== false;
    }

    public static function className2Words(string $className, string $prefixRemove = '', string $postfixRemove = ''): string
    {
        return self::camel2words(self::classNameWithoutNamespace($className, $prefixRemove, $postfixRemove));
    }

    public static function classNameWithoutNamespace(string $className, string $prefixRemove = '', string $postfixRemove = '', bool $whitespaceOnCapital = false): string
    {
        $baseNameWithSeparator = strrchr($className, "\\");

        $baseName = $baseNameWithSeparator === false ? $className : substr($baseNameWithSeparator, 1);

        if ($prefixRemove && str_starts_with($baseName, $prefixRemove)) {
            $baseName = substr($baseName, strlen($prefixRemove));
        }

        if ($postfixRemove && str_ends_with($baseName, $postfixRemove)) {
            $baseName = substr($baseName, 0, -strlen($postfixRemove));
        }

        if ($whitespaceOnCapital) {
            $baseName = preg_replace('#(?<!^)(?=[A-Z])#', ' $0', $baseName);
        }

        return $baseName;
    }

    public static function booleanMixedToBool(mixed $s): ?bool
    {
        return match ($s) {
            null => null,
            'true', 'TRUE', '1', 1, true => true,
            'false', 'FALSE', '0', 0, false => false,
        };
    }

    public static function fancyAssocList(iterable $data, string $perLine = "\n"): string
    {
        $result = [];

        foreach ($data as $k => $v) {
            if (is_array($v)) {
                $v = Json::encode($v, count($v) > 1 ? JSON_PRETTY_PRINT : 0);
            }

            $result[] = "$k: $v";
        }

        return implode($perLine, $result);
    }

    public static function extractEmail(string $emailWithName): string
    {
        if (!preg_match('#([^\s<]+@[^\s>]+)(?:$|>)#', $emailWithName, $emailMatches)) {
            throw new \InvalidArgumentException("Unable to parse emails form $emailWithName");
        }

        return $emailMatches[1];
    }

    public static function extractLanguageFromString(?string $string): ?string
    {
        if (is_null($string) || !preg_match('#^(\w{2,3})(?=$|\W)(?:(-\w{4})(?=$|-|_))?(?:(?:-|_)(\w{2,3})(?=$|\W))?#m', $string, $matches)) {
            return null;
        }
        return $matches[0] ?? null;
    }

    public static function containsLine(string $needle, string $haystack): bool
    {
        return str_contains($haystack, $needle) && in_array($needle, self::explodeText($haystack), true);
    }

    /** 'Lost Deposit' => 'lost_deposit' */
    public static function wordsToSnake(string $words): string
    {
        return preg_replace('#\s+#', '_', mb_strtolower(trim($words)));
    }

    public static function roundUntilNonZero(?string $decimal): ?string
    {
        if ($decimal === null) {
            return null;
        }

        if (!str_contains($decimal, '.')) {
            return $decimal;
        }

        $result = rtrim($decimal, '.0');

        return $result === '' ? '0' : $result;
    }
}
