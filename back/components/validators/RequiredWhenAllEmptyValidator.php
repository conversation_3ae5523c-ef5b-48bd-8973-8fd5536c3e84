<?php

declare(strict_types=1);

namespace app\back\components\validators;

#[\Attribute]
class RequiredWhenAllEmptyValidator extends RequiredWhenBaseValidator
{
    public function validate(mixed $value, $form, array $context): ?string
    {
        if (!$this->isEmpty($value)) {
            return null;
        }

        foreach ($this->paramsNames as $one) {
            if (!$this->isEmpty($context[$one] ?? null)) {
                return null;
            }
        }
        return 'is required';
    }
}
