<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Payments;

use app\back\components\helpers\DateHelper;
use app\back\components\SiteUserBuilder;
use app\back\entities\CanonicalPaySySource;
use app\back\entities\Rate;
use app\back\entities\TrafficSource;
use app\back\entities\UserIgnoreId;
use app\back\entities\UserTransaction;
use app\back\modules\reports\columns\{
    AffDataColumn,
    BooleanColumn,
    BrandColumn,
    CanonicalPaySystemColumn,
    CommentColumn,
    CountColumn,
    CountFilterColumn,
    CountryColumn,
    CountUniqUsersColumn,
    CurrencyColumn,
    CurrencyFilterColumn,
    DateColumn,
    DateTimeColumn,
    DateTimeEESTColumn,
    DateTypeColumn,
    DayColumn,
    DepNumberColumn,
    HostColumn,
    HourColumn,
    IgnoreColumn,
    IpColumn,
    IsFirstSuccessColumn,
    IsFirstTryColumn,
    LocationColumn,
    MarketingTidBannerColumn,
    MarketingTidDealColumn,
    MarketingTidPublisherColumn,
    Minute10Column,
    MoneyColumn,
    MoneyFilterColumn,
    MonthColumn,
    PayGatewayColumn,
    PaySystemColumn,
    PercentColumn,
    PeriodColumn,
    RefcodeAppIdColumn,
    RefcodeColumn,
    RefcodeCrmChannelColumn,
    RefcodeProgramIdColumn,
    RefcodePublisherColumn,
    RequisiteColumn,
    SimpleColumn,
    SiteHostForToxicUserColumn,
    SiteHostIsAppColumn,
    SiteIdColumn,
    SiteUserColumn,
    SocialNetColumn,
    TemplateDescriptionColumn,
    TimeColumn,
    TrafficSourceColumn,
    TsShareColumn,
    UserActiveStatusColumn,
    UseragentAppColumn,
    UseragentAppGroupColumn,
    UseragentBrowserColumn,
    UseragentColumn,
    UseragentDeviceColumn,
    UseragentPlatformColumn,
    UseragentPlatformGroupColumn,
    UseragentPlatformVersionColumn,
    UseragentVariantColumn,
    UseragentVariantVersionColumn,
    UserBettingProfitSegmentColumn,
    UserCidColumn,
    UserFullStatusColumn,
    UserIdColumn,
    UserIsRmColumn,
    UserKycColumn,
    UserLocaleColumn,
    UserLoyaltyStatusColumn,
    UserPersonalManagerColumn,
    UserPriorityTypeOfGamblingColumn,
    UsersStatsAdminCommentColumn,
    UsersStatsBalanceTypeColumn,
    UsersStatsBonusIdColumn,
    UsersStatsDirColumn,
    UsersStatsExtTypeColumn,
    UsersStatsGameGroupColumn,
    UsersStatsOpIdColumn,
    UsersStatsRemoteIdColumn,
    UsersStatsRewardProviderColumn,
    UsersStatsStatusColumn,
    UsersTransactionsTransactionIdColumn,
    UsersStatsTypeColumn,
    UserStatusColumn,
    UserUuidColumn,
    VipAffProgTypeColumn,
    WebmasterAffOwnerColumn,
    WebmasterAffOwnerGroupColumn,
    WebmasterIdColumn,
    WebmasterProgramTypeColumn,
    WeekColumn,
    WeekDayColumn};
use app\back\modules\api\components\Operators;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\repositories\{
    AffData,
    BonusTemplates,
    CanonicalPaySySources,
    Hosts,
    LoyaltyStatuses,
    MarketingTids,
    PaySystems,
    Refcodes,
    UseragentApps,
    UseragentPlatforms,
    Useragents,
    UserBettingProfitSegments,
    UserKycs,
    UserLogins,
    UserLoyalties,
    Users,
    UserSpecialInfos,
    UserTransactions,
    UserTransactionInfos,
    VipaffRefcodes,
    WpPrograms,
    WpWebmasters};
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class PaymentsConfig extends BaseReportConfig
{
    public const array DATE_TYPES_MAP = [
        DateTypeColumn::UPDATED => 'updated_at',
        DateTypeColumn::CREATED => 'created_at',
    ];

    private string $dateColumn = 'updated_at';
    private ?int $tsShare = null;

    protected function beforeQuery(): void
    {
        $this->dateColumn = static::DATE_TYPES_MAP[$this->request->getFilter('date_type') ?? DateTypeColumn::UPDATED];

        if ($this->request->getFilter('log_ts_share')) {
            $this->tsShare = (int)$this->request->getFilter('log_ts_share');
        }
    }

    public function rules(): array
    {
        return [
            ['date', 'required', 'when' => fn() => $this->request->allEmptyFilter('fiscal_date_at', 'date_time')],
            ['site_id', 'required', 'when' => fn() => $this->request->anyEmptyFilter('site_user')],
            ['log_ts_share', 'required', 'when' => fn() => $this->request->anyMetric('share_sum_ts_eur', 'share_sum_ts_percent', 'share_uniq_users_ts', 'share_uniq_users_ts_percent')],
            ['groups', fn() => 'Currency must be grouped when "Sum orig" metric used', 'when' => fn() => $this->request->anyMetric('sum_orig') && !$this->request->anyGroup('currency')],
            [
                ['total_sum_eur', 'total_sum_usd', 'total_count'],
                fn($value) => $value !== null && !$this->request->isTotals ? 'Total filters can be used only with Totals request' : null
            ],
            ['reg_ts', 'required', 'when' => fn() => !$this->allowedLists->isAllTrafficSourcesAllowed()],
            ['op_id', function () {
                if (!$this->request->anyMetric('ngr_eur')) {
                    return null;
                }

                $inOut = [UserTransaction::OP_IN, UserTransaction::OP_OUT];
                $selectedFilter = $this->request->getFilter('op_id') ?? [];
                $isEquals = count($selectedFilter) === count($inOut) && !array_diff($inOut, $selectedFilter);
                return !$isEquals ? "Op type must be only IN and OUT when NGR metric selected" : null;
            }],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', DateHelper::monthBegin(), '>='],
            ['date', DateHelper::yesterday(), '<='],
            ['site_id', []],
            ['date_type', DateTypeColumn::UPDATED],
            ['op_id', [UserTransaction::OP_IN], Operators::IN],
            ['status', [UserTransaction::STATUS_SUCCESS], Operators::IN],
            ['ignore', UserIgnoreId::MODE_IGNORE],
            ['columns', ['site_id', 'user_id', 'transaction_id', 'site_user', 'created_at', 'updated_at', 'op_id', 'type', 'dir', 'balance_type', 'ext_type', 'status', 'sum_usd', 'sum_eur', 'sum_orig', 'currency']],
            ['metrics', ['count', 'sum_eur']],
        ];
    }

    public function filters(): array
    {
        return [
            'Transaction' => array_merge([
                'site_id' => [SiteIdColumn::class, 'us'],
                'date' => [DateColumn::class, ['us' => $this->dateColumn]],
                'date_time' => [DateTimeColumn::class, ['us' => $this->dateColumn], 'title' => 'Date (time)'],
                'time' => [TimeColumn::class, ['us' => $this->dateColumn]],
                'date_type' => [DateTypeColumn::class],
                'currency' => [CurrencyFilterColumn::class, 'us', 'currencies' => Rate::currencies(), 'allowMultiple' => true],
                'template_description' => [TemplateDescriptionColumn::class, 'bt'],
            ], $this->transactionColumns()),
            'User' => array_merge($this->mainColumns(), $this->userColumns(), [
                'ignore' => [IgnoreColumn::class, 'us'],
                'dep_first_at' => [DateColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Date (FD)'],
                'registered_at' => [DateColumn::class, 'u', 'title' => 'Date (reg)'],
                'loyalty_status' => [UserLoyaltyStatusColumn::class, 'ls'],
                'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u'],
                'segment_id' => [UserBettingProfitSegmentColumn::class, 'ubps'],
            ]),
            'Pay' => $this->payColumns(),
            'Login' => array_merge($this->loginColumns(), ['log_ts_share' => [TsShareColumn::class, 'r_log']]),
            'Reg' => $this->regColumns(),
            'Checkbox' => $this->checkboxColumns(),
            'Total filters' => [
                'total_sum_eur' => [MoneyFilterColumn::class, 'us', 'expression' => 'SUM(us.amount_eur)', 'title' => 'Sum EUR', 'having' => true],
                'total_sum_usd' => [MoneyFilterColumn::class, 'us', 'expression' => 'SUM(us.amount_usd)', 'title' => 'Sum USD', 'having' => true],
                'total_count' => [CountFilterColumn::class, 'us', 'expression' => 'COUNT(*)', 'title' => 'Count', 'having' => true],
            ],
        ];
    }

    public function columns(): array
    {
        return [
            'Transaction' => array_merge([
                'site_id' => [SiteIdColumn::class, 'us'],
            ], $this->mainColumns(), [
                'created_at' => [DateColumn::class, ['us' => 'created_at'], 'title' => 'Date (created)'],
                'updated_at' => [DateColumn::class, ['us' => 'updated_at'], 'title' => 'Date (modified)'],
            ], $this->transactionColumns(), [
                'sum_usd' => [MoneyColumn::class, ['us' => 'amount_usd'], 'title' => 'Sum USD'],
                'sum_eur' => [MoneyColumn::class, ['us' => 'amount_eur'], 'title' => 'Sum EUR'],
                'sum_orig' => [MoneyColumn::class, ['us' => 'amount_orig'], 'title' => 'Sum Orig'],
                'sum_fee_profit' => [MoneyColumn::class, ['us' => 'fee_profit'], 'title' => 'Profit fee (orig)'],
                'sum_orig_with_fee_profit' => [MoneyColumn::class, ['expr' => '(us.amount_orig + COALESCE(us.fee_profit, 0))'], 'title' => 'Sum Orig + Profit fee'],
                'sum_fee_wager' => [MoneyColumn::class, ['us' => 'fee_wager'], 'title' => 'Wager fee (orig)'],

                'from_wallet_id' => [SimpleColumn::class, ['us' => 'from_wallet_id'], 'title' => 'Source wallet ID'],
                'to_wallet_id' => [SimpleColumn::class, ['us' => 'to_wallet_id'], 'title' => 'Target wallet ID'],
                'approved_by' => [SimpleColumn::class, ['us' => 'approved_by'], 'title' => 'Approved by'],
                'approved_at' => [DateColumn::class, ['us' => 'approved_at'], 'title' => 'Approved at'],
                'template_description' => [TemplateDescriptionColumn::class, 'bt'],
                'currency' => [CurrencyColumn::class, 'us'],
            ]),
            'Pay' => array_merge($this->payColumns(), [
                'pay_useragent' => [UseragentColumn::class, 'uag_pay'],
                'pay_platform_version' => [UseragentPlatformVersionColumn::class, 'uag_pay'],
                'pay_variant_version' => [UseragentVariantVersionColumn::class, 'uag_pay'],
                'pay_device_id' => [UseragentDeviceColumn::class, 'uag_pay'],
            ]),
            'Login' => array_merge($this->loginColumns(), [
                'log_aff_data' => [AffDataColumn::class, 'ad_log'],
                'log_device_id' => [UseragentDeviceColumn::class, 'uag_log'],
            ]),
            'User' => array_merge($this->userColumns(), [
                'dep_first_at' => [DateColumn::class, ['usi' => 'dep_first_at'], 'title' => 'Date (FD)'],
                'registered_at' => [DateColumn::class, 'u', 'title' => 'Date (reg)'],
                'loyalty_status' => [UserLoyaltyStatusColumn::class, 'ls'],
                'segment_id' => [UserBettingProfitSegmentColumn::class, 'ubps'],
            ]),
            'Reg' => array_merge($this->regColumns(), [
                'reg_aff_data' => [AffDataColumn::class, 'ad_reg'],
                'reg_device_id' => [UseragentDeviceColumn::class, 'uag_reg'],
            ]),
            'Checkbox' => $this->checkboxColumns(),
        ];
    }

    public function metrics(): array
    {
        $tsFilter = ' FILTER (WHERE r_log.ts_id = :tsShare)';
        $sumEur = 'SUM(us.amount_eur)';
        $siteUserExpr = SiteUserBuilder::siteUserQueryExpression('us');
        $shareUniqUsers = "COUNT(DISTINCT $siteUserExpr)";
        $tsShareTitle = 'TS share';
        if ($this->tsShare) {
            $tsShareTitle = TrafficSource::getNameById($this->tsShare);
        }

        return [
            'Sums' => [
                'sum_usd' => [MoneyColumn::class, ['expr' => 'SUM(us.amount_usd)', 'us'], 'title' => 'Sum USD'],
                'sum_eur' => [MoneyColumn::class, ['expr' => $sumEur, 'us'], 'title' => 'Sum EUR'],
                'sum_orig' => [MoneyColumn::class, ['expr' => 'SUM(us.amount_orig)', 'us'], 'title' => 'Sum Orig'],
                'ngr_eur' => [MoneyColumn::class, ['expr' => 'SUM(us.amount_eur * dir)', 'us'], 'title' => 'NGR EUR'],
            ],
            'Counts' => [
                'count' => [CountColumn::class, 'us'],
                'users_count' => [CountUniqUsersColumn::class, 'us'],
            ],
            'Share (TS)' => [
                'share_sum' => [PercentColumn::class, ['expr' => 'SUM(us.amount_usd) / SUM(SUM(us.amount_usd)) OVER () * 100::float', 'us'], 'title' => 'Sum share (%)'],
                'share_count' => [PercentColumn::class, ['expr' => 'COUNT(*) / SUM(COUNT(*)) OVER () * 100::float', 'us'], 'title' => 'Count share (%)'],
                'share_sum_ts_eur' => [MoneyColumn::class, ['expr' => new Expression("$sumEur $tsFilter", ['tsShare' => $this->tsShare]), 'us'], 'title' => $tsShareTitle . ' sum EUR'],
                'share_sum_ts_percent' => [PercentColumn::class, ['expr' => new Expression("$sumEur $tsFilter / NULLIF($sumEur, 0) * 100", ['tsShare' => $this->tsShare]), 'us'], 'title' => $tsShareTitle . ' (%)'],
                'share_uniq_users_ts' => [CountColumn::class, ['expr' => new Expression("$shareUniqUsers $tsFilter", ['tsShare' => $this->tsShare]), 'us'], 'title' => $tsShareTitle . ' unique users'],
                'share_uniq_users_ts_percent' => [PercentColumn::class, ['expr' => new Expression("$shareUniqUsers $tsFilter::float / NULLIF($shareUniqUsers, 0) * 100", ['tsShare' => $this->tsShare]), 'us'], 'title' => $tsShareTitle . ' unique users (%)'],
            ],
            'Periods' => [
                'created_to_modified_period' => [PeriodColumn::class, ['u'], 'innerExpression' => 'AVG(us.updated_at - us.created_at)', 'title' => 'Processing period'],
                'reg_to_first_dep_period' => [PeriodColumn::class, ['u'], 'innerExpression' => 'AVG(us.updated_at - u.date) FILTER (WHERE us.is_first_success)', 'title' => 'Reg to FD period'],
                'first_to_second_pay_period' => [PeriodColumn::class, ['u'], 'innerExpression' => '(ARRAY_AGG(us.updated_at ORDER BY us.updated_at))[2] - (ARRAY_AGG(us.updated_at ORDER BY us.updated_at))[1]', 'title' => 'First to second pay period'],
                'second_to_third_pay_period' => [PeriodColumn::class, ['u'], 'innerExpression' => '(ARRAY_AGG(us.updated_at ORDER BY us.updated_at))[3] - (ARRAY_AGG(us.updated_at ORDER BY us.updated_at))[2]', 'title' => 'Second to third pay period'],
            ],
            'Fees' => [
                'fee_profit' => [MoneyColumn::class, ['expr' => 'SUM(fee_profit)', 'us'], 'title' => 'Profit fee (orig)'],
                'fee_wager' => [MoneyColumn::class, ['expr' => 'SUM(fee_wager)', 'us'], 'title' => 'Wager fee (orig)'],
            ],
        ];
    }

    public function groups(): array
    {
        return [
            'Transaction' => array_merge([
                'site_id' => [SiteIdColumn::class, 'us'],

                'cr_month' => [MonthColumn::class, ['us' => 'created_at'], 'title' => 'Month (created)'],
                'cr_week' => [WeekColumn::class, ['us' => 'created_at'], 'title' => 'Week (created)'],
                'cr_weekday' => [WeekDayColumn::class, ['us' => 'created_at'], 'title' => 'Week day (created)'],
                'cr_day' => [DayColumn::class, ['us' => 'created_at'], 'title' => 'Day (created)'],
                'cr_hour' => [HourColumn::class, ['us' => 'created_at'], 'title' => 'Hour (created)'],
                'cr_minute_10' => [Minute10Column::class, ['us' => 'created_at'], 'title' => '10 minutes (created)'],

                'upd_month' => [MonthColumn::class, ['us' => 'updated_at'], 'title' => 'Month (modified)'],
                'upd_week' => [WeekColumn::class, ['us' => 'updated_at'], 'title' => 'Week (modified)'],
                'upd_weekday' => [WeekDayColumn::class, ['us' => 'updated_at'], 'title' => 'Week day (modified)'],
                'upd_day' => [DayColumn::class, ['us' => 'updated_at'], 'title' => 'Day (modified)'],
                'upd_hour' => [HourColumn::class, ['us' => 'updated_at'], 'title' => 'Hour (modified)'],
                'upd_minute_10' => [Minute10Column::class, ['us' => 'updated_at'], 'title' => '10 minutes (modified)'],
                'template_description' => [TemplateDescriptionColumn::class, 'bt'],
            ], $this->transactionColumns(), [
                'approved_by' => [SimpleColumn::class, ['us' => 'approved_by'], 'title' => 'Approved by'],
                'approved_at' => [DateColumn::class, ['us' => 'approved_at'], 'title' => 'Approved at'],
                'currency' => [CurrencyColumn::class, 'us'],
            ]),
            'User' => array_merge($this->mainColumns(), $this->userColumns(), [
                'user_full_status' => [UserFullStatusColumn::class, 'u'],
                'uuid' => [UserUuidColumn::class, 'u'],
                'loyalty_status' => [UserLoyaltyStatusColumn::class, 'ls'],
                'priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u'],
                'segment_id' => [UserBettingProfitSegmentColumn::class, 'ubps'],
            ]),
            'Pay' => array_merge($this->payColumns(), [
                'pay_app_id' => [UseragentAppColumn::class, 'uag_pay'],
                'pay_publisher_from_refcode' => [RefcodePublisherColumn::class, 'r_pay'],
                'pay_useragent' => [UseragentColumn::class, 'uag_pay'],
                'pay_platform_version' => [UseragentPlatformVersionColumn::class, 'uag_pay'],
                'pay_variant_version' => [UseragentVariantVersionColumn::class, 'uag_pay'],
                'pay_device_id' => [UseragentDeviceColumn::class, 'uag_pay'],
            ]),
            'Login' => array_merge($this->loginColumns(), [
                'log_aff_data' => [AffDataColumn::class, 'ad_log'],
                'log_app_id' => [UseragentAppColumn::class, 'uag_log'],
                'log_publisher_from_refcode' => [RefcodePublisherColumn::class, 'r_log'],
                'log_deal' => [MarketingTidDealColumn::class, 'tids_log'],
                'log_device_id' => [UseragentDeviceColumn::class, 'uag_log'],
            ]),
            'Reg' => array_merge([
                'reg_month' => [MonthColumn::class, 'u'],
                'reg_week' => [WeekColumn::class, 'u'],
                'reg_weekday' => [WeekDayColumn::class, 'u'],
                'reg_day' => [DayColumn::class, 'u'],
                'reg_hour' => [HourColumn::class, 'u'],
            ], $this->regColumns(), [
                'reg_aff_data' => [AffDataColumn::class, 'ad_reg'],
                'reg_app_id' => [UseragentAppColumn::class, 'uag_reg'],
                'reg_publisher_from_refcode' => [RefcodePublisherColumn::class, 'r_reg'],
                'reg_deal' => [MarketingTidDealColumn::class, 'tids_reg'],
                'reg_banner' => [MarketingTidBannerColumn::class, 'tids_reg'],
                'reg_device_id' => [UseragentDeviceColumn::class, 'uag_reg'],
            ]),
            'First dep' => [
                'fd_month' => [MonthColumn::class, ['usi' => 'dep_first_at']],
                'fd_week' => [WeekColumn::class, ['usi' => 'dep_first_at']],
                'fd_day' => [DayColumn::class, ['usi' => 'dep_first_at']],
                'fd_hour' => [HourColumn::class, ['usi' => 'dep_first_at']],
            ],
            'Checkbox' => [
                'fiscal_date_month' => [MonthColumn::class, ['uti' => 'fiscal_date_at']],
                'fiscal_date_week' => [WeekColumn::class, ['uti' => 'fiscal_date_at']],
                'fiscal_date_day' => [DayColumn::class, ['uti' => 'fiscal_date_at']],
                'fiscal_date_hour' => [HourColumn::class, ['uti' => 'fiscal_date_at']],
                'checkout_type' => [CheckoutTypeColumn::class, 'uti'],
            ],
        ];
    }

    public function tableMap(): array
    {
        return [
            'us' => [UserTransactions::TABLE_NAME],
            'u' => [Users::TABLE_NAME, 'u.site_id = us.site_id AND u.user_id = us.user_id'],
            'usi' => [UserSpecialInfos::TABLE_NAME, 'usi.site_id = us.site_id AND usi.user_id = us.user_id'],
            'ul' => [UserLogins::TABLE_NAME, 'ul.site_id = us.site_id AND ul.login_id = us.login_id'],
            'ps' => [PaySystems::TABLE_NAME, 'ps.id = us.pay_sys_id'],
            'cps' => [function (Query $query) {
                $query->addParams(['source' => CanonicalPaySySource::SOURCE_PRODUCT]);
                return CanonicalPaySySources::TABLE_NAME;
            }, 'cps.name = ps.name AND cps.source = :source', ['ps']],
            'uly' => [UserLoyalties::TABLE_NAME, 'uly.site_id = us.site_id AND uly.user_id = us.user_id'],
            'ls' => [LoyaltyStatuses::TABLE_NAME, 'ls.site_id = uly.site_id AND ls.id = COALESCE(uly.status_id, 1)', ['uly']],
            'kyc' => [UserKycs::TABLE_NAME, 'kyc.site_id = us.site_id AND kyc.user_id = us.user_id'],

            'r_pay' => [Refcodes::TABLE_NAME, 'r_pay.id = us.refcode_id'],
            'tids_pay' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r_pay', 'tids_pay'), ['r_pay']],
            'wp_p_pay' => [WpPrograms::TABLE_NAME, 'wp_p_pay.id = ' . Refcodes::programExpression('r_pay'), ['r_pay']],
            'wp_w_pay' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('wp_w_pay', 'r_pay'), ['r_pay']],
            'uag_pay' => [Useragents::TABLE_NAME, 'uag_pay.id = us.useragent_id'],
            'uaga_pay' => [UseragentApps::TABLE_NAME, 'uaga_pay.id = uag_pay.app_id', ['uag_pay']],
            'uagp_pay' => [UseragentPlatforms::TABLE_NAME, 'uagp_pay.id = uag_pay.platform_id', ['uag_pay']],
            'sh_pay' => [Hosts::TABLE_NAME, 'sh_pay.id = us.host_id'],

            'ad_reg' => [AffData::TABLE_NAME, 'ad_reg.id = u.aff_data_id', ['u']],
            'var_reg' => [VipaffRefcodes::TABLE_NAME, 'var_reg.refcode_id = u.refcode_id', ['u']],
            'r_reg' => [Refcodes::TABLE_NAME, 'r_reg.id = u.refcode_id', ['u']],
            'tids_reg' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r_reg', 'tids_reg'), ['r_reg']],
            'wp_p_reg' => [WpPrograms::TABLE_NAME, 'wp_p_reg.id = ' . Refcodes::programExpression('r_reg'), ['r_reg']],
            'wp_w_reg' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('wp_w_reg', 'r_reg'), ['r_reg']],
            'uag_reg' => [Useragents::TABLE_NAME, 'uag_reg.id = u.useragent_id', ['u']],
            'uaga_reg' => [UseragentApps::TABLE_NAME, 'uaga_reg.id = uag_reg.app_id', ['uag_reg']],
            'uagp_reg' => [UseragentPlatforms::TABLE_NAME, 'uagp_reg.id = uag_reg.platform_id', ['uag_reg']],
            'sh_reg' => [Hosts::TABLE_NAME, 'sh_reg.id = u.host_id', ['u']],

            'ad_log' => [AffData::TABLE_NAME, 'ad_log.id = ul.aff_data_id', ['ul']],
            'var_log' => [VipaffRefcodes::TABLE_NAME, 'var_log.refcode_id = ul.refcode_id', ['ul']],
            'r_log' => [Refcodes::TABLE_NAME, 'r_log.id = ul.refcode_id', ['ul']],
            'tids_log' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r_log', 'tids_log'), ['r_log']],
            'wp_p_log' => [WpPrograms::TABLE_NAME, 'wp_p_log.id = ' . Refcodes::programExpression('r_log'), ['r_log']],
            'wp_w_log' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('wp_w_log', 'r_log'), ['r_log']],
            'uag_log' => [Useragents::TABLE_NAME, 'uag_log.id = ul.useragent_id', ['ul']],
            'uaga_log' => [UseragentApps::TABLE_NAME, 'uaga_log.id = uag_log.app_id', ['uag_log']],
            'uagp_log' => [UseragentPlatforms::TABLE_NAME, 'uagp_log.id = uag_log.platform_id', ['uag_log']],
            'sh_log' => [Hosts::TABLE_NAME, 'sh_log.id = ul.host_id', ['ul']],

            'ubps' => [UserBettingProfitSegments::TABLE_NAME, 'ubps.site_id = us.site_id AND ubps.user_id = us.user_id'],
            'bt' => [BonusTemplates::TABLE_NAME, "bt.site_id = us.site_id AND bt.template_id = (NULLIF(us.bonus_id, ''))::uuid"],

            'uti' => [UserTransactionInfos::TABLE_NAME, 'uti.site_id = us.site_id AND uti.transaction_id = us.transaction_id'],
        ];
    }

    private function mainColumns(): array
    {
        return [
            'user_id' => [UserIdColumn::class, 'us'],
            'site_user' => [SiteUserColumn::class, 'us'],
        ];
    }

    private function transactionColumns(): array
    {
        return [
            'transaction_id' => [UsersTransactionsTransactionIdColumn::class, 'us'],
            'remote_id' => [UsersStatsRemoteIdColumn::class, 'us'],
            'op_id' => [UsersStatsOpIdColumn::class, 'us'],
            'type' => [UsersStatsTypeColumn::class, 'us'],
            'is_first_success' => [IsFirstSuccessColumn::class, 'us'],
            'is_first' => [IsFirstTryColumn::class, 'us'],
            'dir' => [UsersStatsDirColumn::class, 'us'],
            'balance_type' => [UsersStatsBalanceTypeColumn::class, 'us'],
            'ext_type' => [UsersStatsExtTypeColumn::class, 'us'],
            'status' => [UsersStatsStatusColumn::class, 'us'],
            'pay_sys_id' => [PaySystemColumn::class, 'us'],
            'canonical_pay_sys_id' => [CanonicalPaySystemColumn::class, 'cps'],
            'ext_pay_sys_id' => [PayGatewayColumn::class, 'us'],
            'comment' => [CommentColumn::class, 'us'],
            'comment_admin' => [UsersStatsAdminCommentColumn::class, 'us'],
            'bonus_id' => [UsersStatsBonusIdColumn::class, ['us' => 'bonus_id'], 'title' => 'Bonus ID'],
            'game_group' => [UsersStatsGameGroupColumn::class, 'us'],
            'reward_provider' => [UsersStatsRewardProviderColumn::class, 'us'],
            'dep_number' => [DepNumberColumn::class, 'us'],
            'wallet' => [RequisiteColumn::class, ['us' => 'wallet'], 'title' => 'Wallet'],
            'acquiring_bank_id' => [AcquiringBankColumn::class, 'us'],
        ];
    }

    private function payColumns(): array
    {
        return [
            'pay_ts' => [TrafficSourceColumn::class, 'r_pay', 'maskRestricted' => false],
            'pay_refcode' => [RefcodeColumn::class, 'r_pay'],
            'pay_ref_app_id' => [RefcodeAppIdColumn::class, 'r_pay'],
            'pay_webmaster_id' => [WebmasterIdColumn::class, 'r_pay'],
            'pay_aff_owner' => [WebmasterAffOwnerColumn::class, 'wp_w_pay', 'refcodeTableAlias' => 'r_pay'],
            'pay_aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w_pay', 'refcodeTableAlias' => 'r_pay'],
            'pay_program_type' => [WebmasterProgramTypeColumn::class, 'wp_p_pay'],
            'pay_publisher' => [MarketingTidPublisherColumn::class, 'tids_pay'],
            'pay_crm_channel' => [RefcodeCrmChannelColumn::class, 'r_pay'],

            'pay_ip' => [IpColumn::class, 'us'],
            'pay_country' => [CountryColumn::class, 'us'],
            'pay_host' => [HostColumn::class, 'us'],
            'pay_host_is_app' => [SiteHostIsAppColumn::class, 'sh_pay'],
            'pay_host_toxic' => [SiteHostForToxicUserColumn::class, 'sh_pay'],
            'pay_platform_id' => [UseragentPlatformColumn::class, 'uag_pay'],
            'pay_platform_group' => [UseragentPlatformGroupColumn::class, 'uagp_pay'],
            'pay_browser_id' => [UseragentBrowserColumn::class, 'uag_pay'],
            'pay_variant_id' => [UseragentVariantColumn::class, 'uag_pay'],
            'pay_app_id' => [UseragentAppColumn::class, 'uag_pay'],
            'pay_app_group_id' => [UseragentAppGroupColumn::class, 'uaga_pay'],
        ];
    }

    private function loginColumns(): array
    {
        return [
            'log_ts' => [TrafficSourceColumn::class, 'r_log', 'maskRestricted' => false],
            'log_refcode' => [RefcodeColumn::class, 'r_log'],
            'log_ref_app_id' => [RefcodeAppIdColumn::class, 'r_log'],
            'log_webmaster_id' => [WebmasterIdColumn::class, 'r_log'],
            'log_aff_owner' => [WebmasterAffOwnerColumn::class, 'wp_w_log', 'refcodeTableAlias' => 'r_log'],
            'log_aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w_log', 'refcodeTableAlias' => 'r_log'],
            'log_program_type' => [WebmasterProgramTypeColumn::class, 'wp_p_log'],
            'log_publisher' => [MarketingTidPublisherColumn::class, 'tids_log'],
            'log_vipaff_prog_type' => [VipAffProgTypeColumn::class, 'var_log'],
            'log_crm_channel' => [RefcodeCrmChannelColumn::class, 'r_log'],

            'log_ip' => [IpColumn::class, 'ul'],
            'log_country' => [CountryColumn::class, 'ul'],
            'log_host' => [HostColumn::class, 'ul'],
            'log_host_is_app' => [SiteHostIsAppColumn::class, 'sh_log'],
            'log_host_toxic' => [SiteHostForToxicUserColumn::class, 'sh_log'],
            'log_platform_id' => [UseragentPlatformColumn::class, 'uag_log'],
            'log_platform_group' => [UseragentPlatformGroupColumn::class, 'uagp_log'],
            'log_browser_id' => [UseragentBrowserColumn::class, 'uag_log'],
            'log_variant_id' => [UseragentVariantColumn::class, 'uag_log'],
            'log_app_id' => [UseragentAppColumn::class, 'uag_log'],
            'log_app_group_id' => [UseragentAppGroupColumn::class, 'uaga_log'],
        ];
    }

    private function userColumns(): array
    {
        return [
            'cid' => [UserCidColumn::class, 'u'],
            'social_net' => [SocialNetColumn::class, 'u'],
            'user_status' => [UserStatusColumn::class, 'u'],
            'user_active_status' => [UserActiveStatusColumn::class, 'u'],
            'is_rm' => [UserIsRmColumn::class, 'u'],
            'personal_manager' => [UserPersonalManagerColumn::class, 'u'],
            'kyc' => [UserKycColumn::class, 'kyc'],
            'segment_id' => [UserBettingProfitSegmentColumn::class, 'ubps'],
            'locale' => [UserLocaleColumn::class, 'u'],
        ];
    }

    private function regColumns(): array
    {
        return [
            'reg_ts' => [TrafficSourceColumn::class, 'r_reg'],
            'reg_refcode' => [RefcodeColumn::class, 'r_reg'],
            'reg_ref_app_id' => [RefcodeAppIdColumn::class, 'r_reg'],
            'reg_webmaster_id' => [WebmasterIdColumn::class, 'r_reg'],
            'reg_aff_owner' => [WebmasterAffOwnerColumn::class, 'wp_w_reg', 'refcodeTableAlias' => 'r_reg'],
            'reg_aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'wp_w_reg', 'refcodeTableAlias' => 'r_reg'],
            'reg_program_type' => [WebmasterProgramTypeColumn::class, 'wp_p_reg'],
            'reg_publisher' => [MarketingTidPublisherColumn::class, 'tids_reg'],
            'reg_vipaff_prog_type' => [VipAffProgTypeColumn::class, 'var_reg'],
            'reg_crm_channel' => [RefcodeCrmChannelColumn::class, 'r_reg'],
            'ref_program_id' => [RefcodeProgramIdColumn::class, 'r_reg'],

            'reg_ip' => [IpColumn::class, 'u'],
            'reg_country' => [CountryColumn::class, 'u'],
            'reg_host' => [HostColumn::class, 'u'],
            'reg_host_is_app' => [SiteHostIsAppColumn::class, 'sh_reg'],
            'reg_brand_id' => [BrandColumn::class, 'u'],
            'reg_host_toxic' => [SiteHostForToxicUserColumn::class, 'sh_reg'],
            'reg_platform_id' => [UseragentPlatformColumn::class, 'uag_reg'],
            'reg_platform_group' => [UseragentPlatformGroupColumn::class, 'uagp_reg'],
            'reg_browser_id' => [UseragentBrowserColumn::class, 'uag_reg'],
            'reg_variant_id' => [UseragentVariantColumn::class, 'uag_reg'],
            'reg_app_id' => [UseragentAppColumn::class, 'uag_reg'],
            'reg_app_group_id' => [UseragentAppGroupColumn::class, 'uaga_reg'],
            'location' => [LocationColumn::class, 'u'],

            'phone_exists' => [BooleanColumn::class,  ['expr' => '(u.phone IS NOT NULL)', 'u'], 'title' => 'Exists phone'],
            'player_link' => [UserIdColumn::class, 'u', 'title' => 'Player link', 'isHtmlValue' => true, 'decorateWithPlayerLink' => true],
        ];
    }

    private function checkboxColumns(): array
    {
        return [
            'fiscal_date_at' => [DateTimeEESTColumn::class, ['uti' => 'fiscal_date_at'], 'title' => 'Fiscal date'],
            'fiscal_number' => [FiscalNumberColumn::class, 'uti'],
            'receipt_id' => [ReceiptIdColumn::class, 'uti'],
            'checkout_type' => [CheckoutTypeColumn::class, 'uti'],
            'is_original_refund' => [BooleanColumn::class, ['uti' => 'is_original_refund'], 'title' => 'Is original refund'],
        ];
    }
}
