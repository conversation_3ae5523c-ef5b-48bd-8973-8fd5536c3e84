<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Games;

use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class LaunchPlacePositionColumn extends BaseColumn implements Selected
{
    public string $title = 'Launch place positions';
    public string $column = 'position';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
