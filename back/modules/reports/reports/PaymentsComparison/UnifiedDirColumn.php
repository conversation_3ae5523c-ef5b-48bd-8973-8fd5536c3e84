<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\PaymentsComparison;

use app\back\components\helpers\Arr;
use app\back\components\validators\StringInArrayValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Filtered;
use Yiisoft\Db\Connection\ConnectionInterface;

class UnifiedDirColumn extends BaseColumn implements Filtered
{
    public const string DIR_IN = 'in';
    public const string DIR_OUT = 'out';

    public string $title = 'Direction';
    public array $directions = [
        self::DIR_IN => 'In',
        self::DIR_OUT => 'Out',
    ];
    public array $dirMap = [];

    public function inputProps(): array
    {
        return [
            'type' => 'select',
            'multiple' => false,
            'list' => Arr::assocToIdName($this->directions),
        ];
    }

    public function rule(): array
    {
        return [StringInArrayValidator::class, $this->directions];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $realDir = $this->dirMap[$value] ?? null;

        if ($realDir === null) {
            return;
        }

        $query->andWhere([$this->columnExpression() => $realDir]);
    }
}
