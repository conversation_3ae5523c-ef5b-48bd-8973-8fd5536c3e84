<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Cohort;

use app\back\modules\reports\columns\BooleanColumn;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\CityColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\DateRangeColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\GenderColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MarketingTidPublisherColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\TrafficSourceColumn;
use app\back\modules\reports\columns\UseragentAppGroupColumn;
use app\back\modules\reports\columns\UseragentPlatformColumn;
use app\back\modules\reports\columns\UseragentPlatformGroupColumn;
use app\back\modules\reports\columns\UseragentVariantColumn;
use app\back\modules\reports\columns\UserIdColumn;
use app\back\modules\reports\columns\UserPriorityTypeOfGamblingColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerGroupColumn;
use app\back\modules\reports\columns\WebmasterIdColumn;
use app\back\modules\reports\columns\WebmasterProgramTypeColumn;
use app\back\modules\reports\components\BaseQueryConfig;
use app\back\repositories\Cities;
use app\back\repositories\MarketingTids;
use app\back\repositories\Refcodes;
use app\back\repositories\UseragentApps;
use app\back\repositories\UseragentPlatforms;
use app\back\repositories\Useragents;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\WpPrograms;
use app\back\repositories\WpWebmasters;

class CohortEntryConfig extends BaseQueryConfig
{
    private array $entryDateTarget;

    protected function beforeQuery(): void
    {
        $this->request->select('site_id', 'user_id', 'entry_date');

        $this->entryDateTarget = match ($this->request->getFilter('filter_type')) {
            CohortEntryTypeColumn::ENTRY_TYPE_FD => ['usi' => 'dep_first_at'],
            CohortEntryTypeColumn::ENTRY_TYPE_REG => ['u' => 'date'],
        };
    }

    public function selects(): array
    {
        return [
            'site_id' => [SiteIdColumn::class, 'u'],
            'user_id' => [UserIdColumn::class, 'u'],
            'entry_date' => [DayColumn::class, $this->entryDateTarget],
        ];
    }

    public function filters(): array
    {
        return [
            'date' => [DateRangeColumn::class, $this->entryDateTarget],
            'site_id' => [SiteIdColumn::class, 'u'],
            'filter_type' => [CohortEntryTypeColumn::class],
            'ignore' => [IgnoreColumn::class, 'u'],

            'reg_country' => [CountryColumn::class, 'u'],
            'reg_city' => [CityColumn::class, 'c'],
            'reg_platform_id' => [UseragentPlatformColumn::class, ['uag_reg']],
            'reg_platform_group' => [UseragentPlatformGroupColumn::class, ['uagp_reg']],
            'reg_ts' => [TrafficSourceColumn::class, ['r_reg']],
            'reg_publisher' => [MarketingTidPublisherColumn::class, ['tids_reg']],
            'reg_affiliate_id' => [WebmasterIdColumn::class, ['r_reg']],
            'reg_aff_owner' => [WebmasterAffOwnerColumn::class, ['wp_w_reg'], 'refcodeTableAlias' => 'r_reg'],
            'reg_aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, ['wp_w_reg'], 'refcodeTableAlias' => 'r_reg'],
            'reg_program_type' => [WebmasterProgramTypeColumn::class, ['wp_reg']],
            'reg_variant_id' => [UseragentVariantColumn::class, ['uag_reg']],
            'reg_app_group_id' => [UseragentAppGroupColumn::class, ['uag_reg']],
            'reg_brand_id' => [BrandColumn::class, 'u'],
            'reg_location' => [LocationColumn::class, 'u'],
            'reg_priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'u'],

            'age' => [CohortAgeColumn::class, ['u', 'usi']],
            'gender' => [GenderColumn::class, 'u'],
            'email_confirm' => [BooleanColumn::class, ['u' => 'email_confirm']],
        ];
    }

    public function tableMap(): array
    {
        return [
            'u' => [Users::TABLE_NAME],
            'usi' => [UserSpecialInfos::TABLE_NAME, 'u.site_id = usi.site_id AND u.user_id = usi.user_id'],
            'c' => [Cities::TABLE_NAME, 'c.id = u.city_id'],
            'uag_reg' => [Useragents::TABLE_NAME, 'uag_reg.id = u.useragent_id'],
            'uagp_reg' => [UseragentPlatforms::TABLE_NAME, 'uag_reg.platform_id = uagp_reg.id', ['uag_reg']],
            'uaga_reg' => [UseragentApps::TABLE_NAME, 'uaga_reg.id = uag_reg.app_id', ['uagp_reg']],
            'r_reg' => [Refcodes::TABLE_NAME, 'r_reg.id = u.refcode_id'],
            'wp_reg' => [WpPrograms::TABLE_NAME, 'wp_reg.id = ' . Refcodes::programExpression('r_reg'), ['r_reg']],
            'wp_w_reg' => [WpWebmasters::TABLE_NAME, WpWebmasters::refcodesJoinCondition('wp_w_reg', 'r_reg'), ['r_reg']],
            'tids_reg' => [MarketingTids::TABLE_NAME, MarketingTids::tidsJoinExpression('r_reg', 'tids_reg'), ['r_reg']],
        ];
    }

    public function groups(): array
    {
        return [];
    }
}
