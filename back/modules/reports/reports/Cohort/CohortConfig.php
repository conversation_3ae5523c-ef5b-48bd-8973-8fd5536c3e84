<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Cohort;

use app\back\components\helpers\DateHelper;
use app\back\entities\UserIgnoreId;
use app\back\modules\reports\columns\BooleanColumn;
use app\back\modules\reports\columns\BrandColumn;
use app\back\modules\reports\columns\CityColumn;
use app\back\modules\reports\columns\CountryColumn;
use app\back\modules\reports\columns\CurrencyColumn;
use app\back\modules\reports\columns\DateRangeColumn;
use app\back\modules\reports\columns\DayColumn;
use app\back\modules\reports\columns\GameTypeGroupColumn;
use app\back\modules\reports\columns\GenderColumn;
use app\back\modules\reports\columns\IgnoreColumn;
use app\back\modules\reports\columns\LocationColumn;
use app\back\modules\reports\columns\MarketingTidPublisherColumn;
use app\back\modules\reports\columns\MonthColumn;
use app\back\modules\reports\columns\SimpleColumn;
use app\back\modules\reports\columns\SiteIdColumn;
use app\back\modules\reports\columns\TrafficSourceColumn;
use app\back\modules\reports\columns\UseragentAppGroupColumn;
use app\back\modules\reports\columns\UseragentPlatformColumn;
use app\back\modules\reports\columns\UseragentPlatformGroupColumn;
use app\back\modules\reports\columns\UseragentVariantColumn;
use app\back\modules\reports\columns\UserPriorityTypeOfGamblingColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerColumn;
use app\back\modules\reports\columns\WebmasterAffOwnerGroupColumn;
use app\back\modules\reports\columns\WebmasterIdColumn;
use app\back\modules\reports\columns\WebmasterProgramTypeColumn;
use app\back\modules\reports\columns\WeekColumn;
use app\back\modules\reports\components\BaseReportConfig;
use app\back\modules\reports\components\QueryRequest;
use app\back\components\StatHelper;
use Yiisoft\Db\Query\Query;

class CohortConfig extends BaseReportConfig
{
    protected const bool FORBIDDEN_SPLIT_AND_GROUP_SAME_COLUMN = false;

    public const string ENTRY_USERS = 'ent';

    public const string MODE_DEP_SUM = 'dep_sum';
    public const string MODE_DEP_RATIO = 'dep_ratio';
    public const string MODE_DEP_COUNT = 'dep_count';
    public const string MODE_DEP_CHANGES = 'dep_changes';
    public const string MODE_DEP_RETENTION_PERCENT = 'dep_retention_percent';
    public const string MODE_DEP_AVG = 'dep_avg';

    public const string MODE_WD_SUM = 'wd_sum';
    public const string MODE_WD_RATIO = 'wd_ratio';
    public const string MODE_WD_PERCENT = 'wd_percent';

    public const string MODE_IN_OUT_SUM = 'in_out_sum';
    public const string MODE_IN_OUT_RATIO = 'in_out_ratio';
    public const string MODE_IN_OUT_CHANGES = 'in_out_changes';

    public const string MODE_BET_SUM = 'bet_sum';
    public const string MODE_BET_RATIO = 'bet_ratio';
    public const string MODE_BET_COUNT = 'bet_count';
    public const string MODE_BET_COUNT_RATIO = 'bet_count_ratio';
    public const string MODE_BET_RETENTION_PERCENT = 'bet_retention_percent';

    public const string MODE_LOGIN_SUM = 'login_sum';
    public const string MODE_LOGIN_COUNT = 'login_count';
    public const string MODE_LOGIN_RETENTION_PERCENT = 'login_retention_percent';

    private string $horizontalPeriod;
    private string $verticalPeriod;

    private string $mode;
    private array $metricTableAlias;

    private array $outDateMetrics = [];
    private array $colExtremum = [];

    protected function beforeQuery(): void
    {
        $this->horizontalPeriod = $this->request->split;
        $this->request->split = null; // Disable split logic after query, we will do it by ourselves below
        $this->verticalPeriod = $this->request->groups[0];

        $this->mode = $this->request->metrics[0];
        $this->metricTableAlias = self::getMetricDependencies($this->mode);

        [$from, $to] = $this->fromTo();
        foreach (DateHelper::getPeriodTruncatedDates($from, $to, $this->horizontalPeriod) as $i => $date) {
            $dayCol = "{$this->horizontalPeriod}_$i";
            $this->outDateMetrics[$dayCol] = [CohortResultColumn::class, $this->metricTableAlias,
                'title' => ucfirst($this->horizontalPeriod) . " $i",
                'periodIndex' => $i,
                'mode' => $this->mode,
                'currency' => $this->request->getFilter('currency'),
                'styleCallback' => $this->styleExtremum($dayCol),
            ];
        }

        $this->request->metrics = ['entry_cnt', ...array_keys($this->outDateMetrics)];
    }

    protected function beforeDecorate(array &$rows, array &$columns): void
    {
        foreach (array_keys($this->outDateMetrics) as $cohortDay) {
            $cohortCol = array_filter(array_column($rows, $cohortDay));
            $this->colExtremum[$cohortDay] = $cohortCol ? ['min' => min($cohortCol), 'max' => max($cohortCol)] : null;
        }
    }

    private function styleExtremum(string $column): \Closure
    {
        return function ($value) use ($column) {
            $cohortDay = $this->colExtremum[$column] ?? null;
            if ($cohortDay && $value !== null) {
                ['min' => $min, 'max' => $max] = $cohortDay;
                $color = StatHelper::getColorForDistribution((float)$value, (float)$max, (float)$min);
                return ['background-color' => $color];
            }

            return [];
        };
    }

    public function rules(): array
    {
        return [
            [['site_id', 'metrics', 'filter_type', 'currency', 'date', 'split', 'groups'], 'required'],
            [['date'], 'required', 'when' => fn() => ($this->request->allEmptyFilter('date_interval'))],
        ];
    }

    public function defaultParams(): array
    {
        return [
            ['date', DateHelper::range(DateHelper::daysAgo(30), DateHelper::today())],
            ['site_id', []],
            ['ignore', UserIgnoreId::MODE_IGNORE],
            ['split', 'day'],
            ['groups', ['day']],
            ['filter_type', 'reg'],
            ['currency', CurrencyColumn::EUR],
            ['metrics', [self::MODE_DEP_SUM]],
        ];
    }

    public function filters(): array
    {
        return [
            'General' => [
                'date' => [DateRangeColumn::class, ['dates:n', 'ent:n', 'pay:nl', 'login:nl', 'game:nl']],
                'date_interval' => [DateIntervalColumn::class],
                'filter_type' => [CohortEntryTypeColumn::class, 'ent:n'],
                'site_id' => [SiteIdColumn::class, ['ent:n', 'pay:nl', 'login:nl', 'game:nl']],
                'currency' => [CurrencyColumn::class, ['pay:nl', 'game:nl'], 'currencies' => [CurrencyColumn::ORIG, CurrencyColumn::EUR, CurrencyColumn::USD]],
                'game_type_group' => [GameTypeGroupColumn::class, 'game:nl'],
                'ignore' => [IgnoreColumn::class, ['ent:n', 'pay:nl', 'login:nl', 'game:nl']],
                'count_daily_avg' => [BooleanColumn::class, 'wrap:n', 'title' => 'Count daily avg.'],
            ],
            'Reg' => [
                'reg_country' => [CountryColumn::class, 'ent:n', 'title' => 'Country'],
                'reg_city' => [CityColumn::class, 'ent:n', 'title' => 'City'],
                'reg_platform_id' => [UseragentPlatformColumn::class, 'ent:n', 'title' => 'Platform'],
                'reg_platform_group' => [UseragentPlatformGroupColumn::class, 'ent:n', 'title' => 'Platform group'],
                'reg_ts' => [TrafficSourceColumn::class, 'ent:n', 'title' => 'Traffic source'],
                'reg_publisher' => [MarketingTidPublisherColumn::class, 'ent:n', 'title' => 'Publisher'],
                'reg_affiliate_id' => [WebmasterIdColumn::class, 'ent:n', 'title' => 'Webmaster'],
                'reg_aff_owner' => [WebmasterAffOwnerColumn::class, 'ent:n', 'title' => 'Aff owner'],
                'reg_aff_owner_group' => [WebmasterAffOwnerGroupColumn::class, 'ent:n', 'title' => 'Aff owner group'],
                'reg_program_type' => [WebmasterProgramTypeColumn::class, 'ent:n', 'title' => 'WP Program type'],
                'reg_variant_id' => [UseragentVariantColumn::class, 'ent:n', 'title' => 'Variant'],
                'reg_app_group_id' => [UseragentAppGroupColumn::class, 'ent:n', 'title' => 'AppGroup'],
                'reg_brand_id' => [BrandColumn::class, 'ent:n', 'title' => 'Brand'],
                'reg_location' => [LocationColumn::class, 'ent:n'],
                'reg_priority_type_of_gambling' => [UserPriorityTypeOfGamblingColumn::class, 'ent:n'],
            ],
            'Profile' => [
                'age' => [CohortAgeColumn::class, 'ent:n', 'title' => 'Age'],
                'gender' => [GenderColumn::class, 'ent:n', 'title' => 'Gender'],
                'email_confirm' => [BooleanColumn::class, 'ent:n', 'title' => 'Email Confirm'],
            ],
        ];
    }

    public function columns(): array
    {
        return [];
    }

    public function metrics(): array
    {
        if ($this->outDateMetrics) {
            return [
                '' => array_merge([
                    'entry_cnt' => [SimpleColumn::class, ['expr' => 'SUM(entry_cnt)', 'wrap'], 'title' => 'Entry users'],
                ], $this->outDateMetrics)
            ];
        }

        return [
            'Totals cumulative' => [
                self::MODE_DEP_SUM => [SimpleColumn::class, 'title' => 'Dep sum'],
                self::MODE_DEP_COUNT => [SimpleColumn::class, 'title' => 'Dep count'],
                self::MODE_IN_OUT_SUM => [SimpleColumn::class, 'title' => 'In-Out sum'],
                self::MODE_BET_SUM => [SimpleColumn::class, 'title' => 'Bet sum'],
                self::MODE_BET_COUNT => [SimpleColumn::class, 'title' => 'Bet count'],
                self::MODE_WD_SUM => [SimpleColumn::class, 'title' => 'Wd sum'],
                self::MODE_LOGIN_COUNT => [SimpleColumn::class, 'title' => 'Login count'],
                self::MODE_LOGIN_SUM => [SimpleColumn::class, 'title' => 'Login sum'],
            ],
            'By user' => [
                self::MODE_DEP_RATIO => [SimpleColumn::class, 'title' => 'Dep ratio'],
                self::MODE_WD_RATIO => [SimpleColumn::class, 'title' => 'Wd ratio'],
                self::MODE_BET_RATIO => [SimpleColumn::class, 'title' => 'Bet ratio'],
                self::MODE_BET_COUNT_RATIO => [SimpleColumn::class, 'title' => 'Bet count ratio'],
                self::MODE_IN_OUT_RATIO => [SimpleColumn::class, 'title' => 'In-Out ratio'],
            ],
            'Metrics' => [
                self::MODE_DEP_CHANGES => [SimpleColumn::class, 'title' => 'Dep changes'],
                self::MODE_IN_OUT_CHANGES => [SimpleColumn::class, 'title' => 'In-Out changes'],
                self::MODE_LOGIN_RETENTION_PERCENT => [SimpleColumn::class, 'title' => 'Retention percent (Login)'],
                self::MODE_DEP_RETENTION_PERCENT => [SimpleColumn::class, 'title' => 'Retention percent (Dep)'],
                self::MODE_BET_RETENTION_PERCENT => [SimpleColumn::class, 'title' => 'Retention percent (Bet)'],
                self::MODE_DEP_AVG => [SimpleColumn::class, 'title' => 'Avg dep'],
                self::MODE_WD_PERCENT => [SimpleColumn::class, 'title' => 'Wd percent'],
            ],
        ];
    }

    public function groups(): array
    {
        $expr = ['expr' => 'wrap.entry_date', 'wrap'];

        return [
            'Period' => [
                'month' => [MonthColumn::class, $expr],
                'week' => [WeekColumn::class, $expr],
                'day' => [DayColumn::class, $expr],
            ]
        ];
    }

    public function tableMap(): array
    {
        return [
            'dates' => [function (Query $query, QueryRequest $nestedRequest) {
                $this->fillDateFromInterval($nestedRequest);
                $query->withQuery((new CohortDatesConfig($this->container, $this->verticalPeriod, $this->horizontalPeriod))->query($this->db, $nestedRequest), 'dates');
            }],
            'ent' => [function (Query $query, QueryRequest $nestedRequest) {
                $this->fillDateFromInterval($nestedRequest);
                $query->withQuery((new CohortEntryConfig($this->container))->query($this->db, $nestedRequest), static::ENTRY_USERS);
            }],
            'pay' => [function (Query $query, QueryRequest $nestedRequest) {
                $this->fillDateFromInterval($nestedRequest);
                $query->withQuery((new CohortPaymentsConfig($this->container, $this->mode, $this->verticalPeriod, $this->horizontalPeriod))->query($this->db, $nestedRequest), 'pay');
            }],
            'login' => [function (Query $query, QueryRequest $nestedRequest) {
                $this->fillDateFromInterval($nestedRequest);
                $query->withQuery((new CohortLoginsConfig($this->container, $this->verticalPeriod, $this->horizontalPeriod))->query($this->db, $nestedRequest), 'login');
            }],
            'game' => [function (Query $query, QueryRequest $nestedRequest) {
                $this->fillDateFromInterval($nestedRequest);
                $query->withQuery((new CohortGamesConfig($this->container, $this->mode, $this->verticalPeriod, $this->horizontalPeriod))->query($this->db, $nestedRequest), 'game');
            }],
            'wrap' => [function (Query $query, QueryRequest $nestedRequest) {
                $query->withQuery((new CohortWrapConfig($this->container, reset($this->metricTableAlias), $this->verticalPeriod))->query($this->db, $nestedRequest), 'wrap');
                return 'wrap';
            }],
        ];
    }

    private function fillDateFromInterval(QueryRequest $queryRequest): void
    {
        $dateInterval = $this->request->getFilter('date_interval');
        if (empty($dateInterval)) {
            return;
        }

        [$from, $to] = $this->fromTo();
        $queryRequest->addFilter('date', $from, '>=');
        $queryRequest->addFilter('date', $to, '<');
    }

    private function fromTo(): array
    {
        $dateInterval = $this->request->getFilter('date_interval');
        if (empty($dateInterval)) {
            return DateHelper::splitRange($this->request->getFilter('date'));
        }

        preg_match('#(?<num>\d+)(?<period>(day|week|month))#i', $dateInterval, $matches);

        $to = DateHelper::getFirstDayOf(date('Y-m-d'), $matches['period']);
        $from = date('Y-m-d', strtotime("-{$matches['num']} {$matches['period']}", strtotime($to)));

        return [$from, DateHelper::prevDay($to)];
    }

    private static function getMetricDependencies(string $mode): array
    {
        return match ($mode) {
            self::MODE_DEP_COUNT,
            self::MODE_DEP_SUM,
            self::MODE_DEP_RATIO,
            self::MODE_WD_SUM,
            self::MODE_WD_RATIO,
            self::MODE_IN_OUT_SUM,
            self::MODE_IN_OUT_RATIO,
            self::MODE_DEP_CHANGES,
            self::MODE_IN_OUT_CHANGES,
            self::MODE_DEP_RETENTION_PERCENT => ['pay'],
            self::MODE_DEP_AVG => ['pay', 'pay:n' => 'cnt', 'wrap:n' => 'cnt'],
            self::MODE_WD_PERCENT => ['pay', 'pay:n' => 'sum_in', 'wrap:n' => 'sum_in'],

            self::MODE_LOGIN_RETENTION_PERCENT,
            self::MODE_LOGIN_SUM,
            self::MODE_LOGIN_COUNT => ['login'],

            self::MODE_BET_SUM,
            self::MODE_BET_RATIO,
            self::MODE_BET_COUNT,
            self::MODE_BET_COUNT_RATIO,
            self::MODE_BET_RETENTION_PERCENT => ['game'],
        };
    }
}
