<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\BonusJournal;

use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\ColumnQuery;
use app\back\modules\reports\columns\Filtered;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class RedeemCodeIdentifierColumn extends BaseColumn implements Filtered, Selected, Operators
{
    public string $title = 'Redeem code identifier';
    public string $column = 'redeem_code_identifier';

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
            'placeholder' => 'Use ⏎ for multiple values',
        ];
    }

    public function operators(): array
    {
        return Operators::IN_NOT_IN;
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $values = Str::explodeText($value);
        $this->filterInNotIn($db, $query, $values, $operator);
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }

    public function width(): int
    {
        return 6;
    }
}
