<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Domains;

use app\back\modules\reports\columns\BaseIdColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use Yiisoft\Db\Connection\ConnectionInterface;

class ServiceAccountPayOptionsColumn extends BaseIdColumn
{
    public string $title = 'Pay options';
    public string $column = 'pay_options';
    public bool $stringsAllowed = true;

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
