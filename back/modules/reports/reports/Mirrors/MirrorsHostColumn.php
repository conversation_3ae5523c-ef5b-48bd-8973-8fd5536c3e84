<?php

declare(strict_types=1);

namespace app\back\modules\reports\reports\Mirrors;

use app\back\components\validators\StringMultilineValidator;
use app\back\modules\reports\columns\BaseColumn;
use app\back\modules\reports\columns\QueryParamsBag;
use app\back\modules\reports\columns\Input;
use app\back\modules\reports\columns\Selected;
use Yiisoft\Db\Connection\ConnectionInterface;

class MirrorsHostColumn extends BaseColumn implements Input, Selected
{
    public string $title = 'Host (mirror)';

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
            'placeholder' => 'Use ⏎ for multiple values',
        ];
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
