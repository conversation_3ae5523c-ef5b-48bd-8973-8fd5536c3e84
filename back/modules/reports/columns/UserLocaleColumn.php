<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\helpers\Str;
use app\back\components\validators\StringMultilineValidator;
use Yiisoft\Db\Connection\ConnectionInterface;

class UserLocaleColumn extends BaseColumn implements Selected, Filtered
{
    use FilterAndSelectDefault;

    public string $column = 'locale';
    public string $title = 'Locale';

    public function inputProps(): array
    {
        return [
            'type' => 'text-area',
            'placeholder' => 'Use ⏎ for multiple values',
        ];
    }

    public function rule(): array
    {
        return [StringMultilineValidator::class];
    }

    public function filter(ConnectionInterface $db, ColumnQuery $query, $value, ?string $operator = null): void
    {
        $query->andWhere([$this->selectExpression($db, $query) => Str::explodeText($value)]);
    }
}
