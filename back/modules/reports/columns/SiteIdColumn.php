<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\components\AllowedLists;
use app\back\components\validators\AllowedSitesValidator;
use app\back\components\validators\AllowedSiteValidator;
use app\back\repositories\Sites;

class SiteIdColumn extends BaseColumn implements Decorated, Selected, Filtered, OperatorComponent
{
    use FilterAndSelectDefault;

    public string $column = 'site_id';
    public bool $isShort = false;
    public bool $isMultiSelect = true;

    public string $title = 'Site';

    public function __construct(
        protected AllowedLists $allowedLists,
        protected Sites $sites
    ) {
    }

    public function width(): int
    {
        return 6;
    }

    public function inputProps(): array
    {
        return array_merge([
            'type' => 'select',
            'multiple' => $this->isMultiSelect,
        ], $this->allowedLists->getSitesWithGroups());
    }

    public function operatorComponentProps(): array
    {
        return array_merge([
            'type' => 'group-select',
        ], $this->allowedLists->getSitesWithGroups(true));
    }

    public function rule(): array
    {
        return [$this->isMultiSelect ? AllowedSitesValidator::class : AllowedSiteValidator::class];
    }

    public function decorate($value, array $row)
    {
        return $this->isShort ? $this->sites->getShortNameById($value) : $this->sites->getNameById($value);
    }
}
