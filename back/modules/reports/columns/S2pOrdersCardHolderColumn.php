<?php

declare(strict_types=1);

namespace app\back\modules\reports\columns;

use app\back\modules\reports\columns\decorators\MaskDecorator;
use Yiisoft\Db\Connection\ConnectionInterface;

class S2pOrdersCardHolderColumn extends BaseColumn implements Selected, Decorated
{
    use MaskDecorator;

    public string $title = 'Card holder';
    public string $column = 'card_holder';

    public function selectExpression(ConnectionInterface $db, QueryParamsBag $paramsBag): string
    {
        return $this->columnExpression();
    }
}
