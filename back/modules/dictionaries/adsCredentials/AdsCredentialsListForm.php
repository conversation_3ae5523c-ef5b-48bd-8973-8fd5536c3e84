<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\adsCredentials;

use app\back\components\helpers\Arr;
use app\back\components\RichTable;
use app\back\components\validators\BooleanArrayValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\AdStat;
use app\back\repositories\AdAccounts;
use app\back\repositories\AdCredentials;
use app\back\repositories\Proxies;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class AdsCredentialsListForm
{
    use RichTable;

    #[StringValidator(0, 500)]
    public ?string $value = null;
    #[IntArrayValidator(AdStat::PLATFORMS)]
    public array $platformId = [];
    #[StringValidator(0, 500)]
    public ?string $comment = null;
    #[StringValidator(0, 500)]
    public ?string $name = null;
    #[BooleanArrayValidator]
    public array $isActive = [];

    public function __construct(private readonly ConnectionInterface $db)
    {
        $this->pageSize = 500;
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'ID', 'code' => 'id'],
            ['name' => 'Name', 'slotName' => 'name'],
            ['name' => 'Accounts', 'code' => 'accounts'],
            ['name' => 'Value', 'slotName' => 'value'],
            ['name' => 'Active', 'slotName' => 'active'],
            ['name' => 'Last load', 'code' => 'lastLoadAt'],
            ['name' => 'Platform', 'code' => 'platformName'],
            ['name' => 'Comment', 'slotName' => 'comment'],
            ['name' => 'Proxy', 'slotName' => 'proxy'],
            ['name' => 'Actions', 'slotName' => 'actions'],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(2, 'name', 'Name', ['focusOnMount' => true]),
                $this->textInputCell(2, 'value', 'Value'),
                $this->selectBooleanCell(2, 'isActive', 'Active'),
                $this->listCell(4, 'platformId', 'Platform', [
                    'list' => Arr::assocToIdName(AdStat::PLATFORMS),
                ]),
                $this->textInputCell(2, 'comment', 'Comment'),
            ],
        ];
    }

    public function data(): array
    {
        $accountsSubQuery = (new Query($this->db))
            ->select(['accounts' => "COUNT(*)"])
            ->from(['aa' => AdAccounts::TABLE_NAME])
            ->where('aa.credential_id = ac.id');

        $query = (new Query($this->db))
            ->select([
                'id' => 'ac.id',
                'name' => 'ac.name',
                'value' => 'ac.value',
                'platformId' => 'ac.platform_id',
                'isActive' => 'ac.is_active',
                'lastLoadAt' => 'ac.last_load_at',
                'comment' => "ac.comment",
                'proxyConfig' => "p.config",
                'accounts' => $accountsSubQuery,
            ])
            ->from(['ac' => AdCredentials::TABLE_NAME])
            ->leftJoin(['p' => Proxies::TABLE_NAME], 'p.id = ac.proxy_id')
            ->orderBy(['ac.id' => SORT_ASC])
            ->offset($this->getOffset())
            ->limit($this->getLimit());

        $this->applyFilters($query);

        $rows = $query->all();

        foreach ($rows as &$row) {
            $row['platformName'] = AdStat::getPlatformNameById($row['platformId']);
            $row['name'] ??= '';
            $row['comment'] ??= '';
            $row['proxyConfig'] ??= '';
        }

        return $rows;
    }

    protected function total(): int
    {
        $total = (new Query($this->db))
            ->select('COUNT(*)')
        ->from(['ac' => AdCredentials::TABLE_NAME]);

        $this->applyFilters($total);

        return (int) $total->scalar();
    }

    private function applyFilters(Query $query): void
    {
        if ($this->value) {
            $query->andFilterWhere(['ac.value' => trim($this->value)]);
        }

        if ($this->comment) {
            $query->andFilterWhere(['ILIKE', 'ac.comment', trim($this->comment)]);
        }

        if ($this->name) {
            $query->andFilterWhere(['ILIKE', 'ac.name', trim($this->name)]);
        }

        if ($this->isActive) {
            $query->andFilterWhere(['ac.is_active' => $this->isActive]);
        }

        if ($this->platformId) {
            $query->andFilterWhere(['ac.platform_id' => $this->platformId]);
        }
    }
}
