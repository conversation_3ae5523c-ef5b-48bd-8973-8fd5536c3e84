<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\adsCredentials;

use app\back\components\Form;
use app\back\components\validators\IdValidator;
use app\back\repositories\AdCredentials;

class AdsCredentialsDeleteForm
{
    use Form;

    #[IdValidator]
    public int $id;

    public function __construct(
        private readonly AdCredentials $adCredentialsRepo,
    ) {
    }

    public function delete(): void
    {
        $entity = $this->adCredentialsRepo->findOneOr404(['id' => $this->id]);
        $this->adCredentialsRepo->delete($entity);
    }
}
