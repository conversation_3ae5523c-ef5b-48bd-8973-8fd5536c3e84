<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\adsCredentials;

use app\back\components\Form;
use app\back\components\PropNameInErrorMessage;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\ProxyFullUrlValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\AdCredential;
use app\back\entities\Proxy;
use app\back\repositories\AdCredentials;
use app\back\repositories\Proxies;

class AdsCredentialsModifyProxyForm
{
    use Form;

    #[IdValidator]
    public int $id;
    #[StringValidator(0, 500)]
    #[ProxyFullUrlValidator]
    #[CallableValidator([self::class, 'validateProxyIsActive'])]
    #[PropNameInErrorMessage('Proxy')]
    public ?string $proxyConfig = null;

    private Proxy $proxy;

    public function __construct(
        private readonly AdCredentials $adCredentialsRepo,
        private readonly Proxies $proxiesRepo,
    ) {
    }

    public function save(): void
    {
        /** @var AdCredential $entity */
        $entity = $this->adCredentialsRepo->findOneOr404(['id' => $this->id]);
        $entity->proxy_id = $this->proxy->id ?? null;
        $this->adCredentialsRepo->update($entity, ['proxy_id']);
    }

    public static function validateProxyIsActive(?string $value, self $form): ?string
    {
        if ($value === null) {
            return null;
        }

        $proxy = $form->findOrCreateProxy($value, $form);

        if ($proxy !== null && !$proxy->is_active) {
            return $proxy->id . ' is inactive!';
        }
        $form->proxy = $proxy;
        return null;
    }

    private function findOrCreateProxy(string $value, self $form): ?Proxy
    {
        $attributes = [
            'group' => Proxy::GROUP_STP_ADS,
            'config' => $value,
        ];

        $proxy = $form->proxiesRepo->findOne($attributes);

        if ($proxy === null) {
            $proxy = new Proxy();
            $proxy->group = Proxy::GROUP_STP_ADS;
            $proxy->config = $value;
            $proxy->is_active = true;

            $form->proxiesRepo->insert($proxy);
        }

        return $proxy;
    }
}
