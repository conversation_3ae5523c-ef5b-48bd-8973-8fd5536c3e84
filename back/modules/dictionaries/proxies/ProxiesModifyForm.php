<?php

declare(strict_types=1);

namespace app\back\modules\dictionaries\proxies;

use app\back\components\Form;
use app\back\components\PropNameInErrorMessage;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\ProxyFullUrlValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\repositories\Proxies;

class ProxiesModifyForm
{
    use Form;

    #[IdValidator]
    public int $id;
    #[StringValidator(0, 500)]
    #[ProxyFullUrlValidator]
    #[PropNameInErrorMessage('Proxy')]
    public string $config;
    #[StringValidator(0, 5000)]
    public ?string $comment = null;
    #[BooleanValidator]
    public ?bool $is_active = null;
    #[StringInArrayValidator(['comment', 'config', 'is_active'], true)]
    public string $type;

    public function __construct(
        private readonly Proxies $proxies,
    ) {
    }

    public function save(): void
    {
        $type = $this->type;

        $entity = $this->proxies->findOneOr404(['id' => $this->id]);
        $entity->$type = $this->$type;
        $this->proxies->update($entity, [$type]);
    }
}
