<?php

declare(strict_types=1);

namespace app\back\modules\tools\serviceAccountsUpload;

use app\back\components\BaseCsvUploadForm;
use app\back\components\BaseCsvUploadRow;
use app\back\entities\ServiceAccount;
use app\back\repositories\ServiceAccounts;

class ServiceAccountsUploadForm extends BaseCsvUploadForm
{
    public function __construct(
        private readonly ServiceAccountsUploadRow $serviceAccountsUploadRow,
        private readonly ServiceAccountsUploadHandler $serviceAccountsUploadHandler,
    ) {
    }

    protected static function uniqueColumns(): array
    {
        return ServiceAccounts::PRIMARY_KEY;
    }

    protected function rowForm(): BaseCsvUploadRow
    {
        return $this->serviceAccountsUploadRow;
    }

    protected function rowHandlers(): array
    {
        return [$this->serviceAccountsUploadHandler];
    }

    public static function csvColumnsMap(): array
    {
        return ServiceAccount::ALIASES;
    }

    protected function example(): array
    {
        return [
            [
                'type' => 'Domain',
                'account_id' => 'godaddy.com_005',
                'pay_options' => '',
                'holder' => 'Bob',
                'holder_type' => ServiceAccount::HOLDER_TYPES[ServiceAccount::HOLDER_TYPE_PERSONAL],
                'legality' => 'White',
                'pay_method' => 'Bitcoin',
                'pay_holder' => 'Bob',
                'pay_holder_type' => ServiceAccount::HOLDER_TYPES[ServiceAccount::HOLDER_TYPE_PERSONAL],
                'pay_requisites' => '123438******1234',
                'pay_comment' => 'тел +********',
                'pay_amount' => '0.1',
                'pay_currency' => 'BTC',
                'pay_period' => ServiceAccount::PAY_PERIODS[ServiceAccount::PAY_PERIOD_MONTH],
                'pay_date' => '2021-12-15',
                'project' => 'Qwe',
                'comment' => '',
            ],
            [
                'type' => 'Domain',
                'account_id' => 'namecheap.com_003',
                'pay_options' => 'CCard/Paypal',
                'holder' => 'Alice',
                'holder_type' => ServiceAccount::HOLDER_TYPES[ServiceAccount::HOLDER_TYPE_BUSINESS],
                'legality' => 'Grey',
                'pay_method' => 'Bank card',
                'pay_holder' => 'Alice',
                'pay_holder_type' => ServiceAccount::HOLDER_TYPES[ServiceAccount::HOLDER_TYPE_BUSINESS],
                'pay_requisites' => 'f43f2347fg-2qwdq-dqewdas-das',
                'pay_comment' => 'Requisite note',
                'pay_amount' => '100.0',
                'pay_currency' => 'USD',
                'pay_period' => ServiceAccount::PAY_PERIODS[ServiceAccount::PAY_PERIOD_YEAR],
                'pay_date' => '2022-01-26',
                'project' => 'Asd',
                'comment' => 'comment for account',
            ],
        ];
    }

    protected function dictionaries(): array
    {
        return [
            '*' . $this->alias('type') => array_values(ServiceAccount::TYPES),
            '*' . $this->alias('account_id') => ['moniker.com_007', 'namecheap.com_001', 'cloudflare.com_002', '...'],
            //$this->alias('pay_options') => ['CCard/Paypal', 'any text', '...'],
            $this->alias('legality') => array_values(ServiceAccount::LEGALITY),
            //$this->alias('holder') => ['Bob', 'Alice', 'any name', '...'],
            $this->alias('holder_type') => array_values(ServiceAccount::HOLDER_TYPES),
            //$this->alias('pay_holder') => ['Bob', 'Alice', 'any name', '...'],
            $this->alias('pay_holder_type') => array_values(ServiceAccount::HOLDER_TYPES),
            $this->alias('pay_requisites') => ['123438******1234', 'any text', '...'],
            $this->alias('pay_amount') => ['100.0', '1.25', '1000', '...'],
            $this->alias('pay_currency') => ['USD', 'EUR', 'RUB', 'UAH', 'PLN', 'BTC', 'ETH', '...'],
            $this->alias('pay_period') => array_values(ServiceAccount::PAY_PERIODS),
            $this->alias('pay_date') => ['YYYY-MM-DD', '2022-01-26'],
            $this->alias('project') => ['any short text', '...'],
        ];
    }
}
