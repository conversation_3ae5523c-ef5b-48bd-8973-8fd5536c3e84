<?php

declare(strict_types=1);

namespace app\back\modules\tools\cidVisualisation;

class CidVisIconManager
{
    private const array ICONS = [
        'user' => 'user-normal.png',
        'user-toxic' => 'user-toxic.png',
        'multi-source-cluster' => 'incomplete-source.png',
        'email' => 'email.png',
        'email-soc' => 'email-soc.png',
        'email-alt' => 'email-alt.png',
        'email-inactive' => 'email-inactive.png',
        'req-cc' => 'req-cc.png',
        'req-qiwi' => 'req-qiwi.png',
        'req-webmoney' => 'req-webmoney.png',
        'req-iban' => 'req-iban.png',
        'req-other' => 'req-other.png',
        'soc-facebook' => 'soc-facebook.png',
        'soc-google' => 'soc-google.png',
        'soc-mailru' => 'soc-mailru.png',
        'soc-ok' => 'soc-ok.png',
        'soc-vk' => 'soc-vk.png',
        'soc-yandex' => 'soc-yandex.png',
        'soc-other' => 'soc-other.png',
        'special-wmid' => 'special-wmid.png',
        'special-hidden' => 'question-sign.png',
        'id-card' => 'id-card.png',
        'tax-number' => 'tax-number.png',

        'attr-vip' => 'attr-vip.png',
        'attr-contacted' => 'attr-contacted.png',
        'attr-blocked' => 'attr-blocked.png',
        'attr-chargeback' => 'attr-chargeback.png',
        'attr-prev-blocked' => 'attr-prev-blocked.png',
        'attr-test' => 'attr-test.png',
        'attr-verified' => 'attr-verified.png',
        'attr-bonus-black-list' => 'attr-bonus-black-list.png',

        'cluster-email' => 'cluster-email.png',
        'cluster-req-cc' => 'cluster-req-cc.png',
        'cluster-req-qiwi' => 'cluster-req-qiwi.png',
        'cluster-req-webmoney' => 'cluster-req-webmoney.png',
        'cluster-req-other' => 'cluster-req-other.png',
        'cluster-soc-facebook' => 'cluster-soc-facebook.png',
        'cluster-soc-google' => 'cluster-soc-google.png',
        'cluster-soc-mailru' => 'cluster-soc-mailru.png',
        'cluster-soc-ok' => 'cluster-soc-ok.png',
        'cluster-soc-vk' => 'cluster-soc-vk.png',
        'cluster-soc-yandex' => 'cluster-soc-yandex.png',
        'cluster-soc-other' => 'cluster-soc-other.png',
        'cluster-special-wmid' => 'cluster-special-wmid.png',
        'cluster-special-hidden' => 'cluster-question-sign.png',
        'cluster-id-card' => 'cluster-id-card.png',
        'cluster-tax-number' => 'cluster-tax-number.png',
    ];

    private const array ICONS_TYPES = [
        'multi-source-cluster' => 'multi-source-cluster',
        CidVisNodeFactory::TYPE_USER => 'user',
        CidVisNodeFactory::TYPE_USER_TOXIC => 'user-toxic',
        CidVisNodeFactory::TYPE_HIDDEN => 'special-hidden',
        CidVisNodeFactory::TYPE_EMAIL => 'email',
        CidVisNodeFactory::TYPE_WM_ID => 'special-wmid',
        CidVisNodeFactory::TYPE_FACE => 'id-card',
        CidVisNodeFactory::TYPE_REQUISITE_OTHER => 'req-other',
        CidVisNodeFactory::TYPE_REQUISITE_QIWI => 'req-qiwi',
        CidVisNodeFactory::TYPE_REQUISITE_CC => 'req-cc',
        CidVisNodeFactory::TYPE_REQUISITE_WM => 'req-webmoney',
        CidVisNodeFactory::TYPE_REQUISITE_IBAN => 'req-iban',
        CidVisNodeFactory::TYPE_SOCIAL_VK => 'soc-vk',
        CidVisNodeFactory::TYPE_SOCIAL_ODNOKLASNIKI => 'soc-ok',
        CidVisNodeFactory::TYPE_SOCIAL_MAIL_RU => 'soc-mailru',
        CidVisNodeFactory::TYPE_SOCIAL_FACEBOOK => 'soc-facebook',
        CidVisNodeFactory::TYPE_SOCIAL_YANDEX => 'soc-yandex',
        CidVisNodeFactory::TYPE_SOCIAL_GOOGLE => 'soc-google',
        CidVisNodeFactory::TYPE_SOCIAL_OTHER => 'soc-other',
        CidVisNodeFactory::TYPE_REQ_ATTR_BLOCKED => 'attr-blocked',
        CidVisNodeFactory::TYPE_REQ_ATTR_PREV_BLOCKED => 'attr-prev-blocked',
        CidVisNodeFactory::TYPE_REQ_ATTR_CHARGEBACK => 'attr-chargeback',
        CidVisNodeFactory::TYPE_USER_ATTR_VIP => 'attr-vip',
        CidVisNodeFactory::TYPE_USER_ATTR_CONTACTED => 'attr-contacted',
        CidVisNodeFactory::TYPE_USER_ATTR_BLOCKED => 'attr-blocked',
        CidVisNodeFactory::TYPE_USER_ATTR_TEST => 'attr-test',
        CidVisNodeFactory::TYPE_USER_ATTR_VERIFIED => 'attr-verified',
        CidVisNodeFactory::TYPE_USER_ATTR_BONUS_BLACK_LIST => 'attr-bonus-black-list',
        CidVisNodeFactory::TYPE_USER_SPECIAL_SOCIAL_EMAIL => 'email-soc',
        CidVisNodeFactory::TYPE_USER_SPECIAL_ALT_EMAIL => 'email-alt',
        CidVisNodeFactory::TYPE_INACTIVE_EMAIL => 'email-inactive',
        CidVisNodeFactory::TYPE_TAX_NUMBER => 'tax-number',
        'cluster-' . CidVisNodeFactory::TYPE_HIDDEN => 'cluster-special-hidden',
        'cluster-' . CidVisNodeFactory::TYPE_EMAIL => 'cluster-email',
        'cluster-' . CidVisNodeFactory::TYPE_WM_ID => 'cluster-special-wmid',
        'cluster-' . CidVisNodeFactory::TYPE_FACE => 'cluster-id-card',
        'cluster-' . CidVisNodeFactory::TYPE_REQUISITE_OTHER => 'cluster-req-other',
        'cluster-' . CidVisNodeFactory::TYPE_REQUISITE_QIWI => 'cluster-req-qiwi',
        'cluster-' . CidVisNodeFactory::TYPE_REQUISITE_CC => 'cluster-req-cc',
        'cluster-' . CidVisNodeFactory::TYPE_REQUISITE_WM => 'cluster-req-webmoney',
        'cluster-' . CidVisNodeFactory::TYPE_SOCIAL_VK => 'cluster-soc-vk',
        'cluster-' . CidVisNodeFactory::TYPE_SOCIAL_ODNOKLASNIKI => 'cluster-soc-ok',
        'cluster-' . CidVisNodeFactory::TYPE_SOCIAL_MAIL_RU => 'cluster-soc-mailru',
        'cluster-' . CidVisNodeFactory::TYPE_SOCIAL_FACEBOOK => 'cluster-soc-facebook',
        'cluster-' . CidVisNodeFactory::TYPE_SOCIAL_YANDEX => 'cluster-soc-yandex',
        'cluster-' . CidVisNodeFactory::TYPE_SOCIAL_GOOGLE => 'cluster-soc-google',
        'cluster-' . CidVisNodeFactory::TYPE_SOCIAL_OTHER => 'cluster-soc-other',
        'cluster-' . CidVisNodeFactory::TYPE_TAX_NUMBER => 'cluster-tax-number',
    ];

    private const array ICONS_DESCRIPTION = [
        'user' => 'User',
        'email' => 'Email',
        'email-soc' => 'Social email',
        'id-card' => 'Recognized similar faces from documents',

        'req-cc' => 'Requisite Credit Card',
        'req-qiwi' => 'Requisite Qiwi',
        'req-webmoney' => 'Requisite Webmoney',
        'req-other' => 'Requisite other',

        'soc-facebook' => 'Social Facebook',
        'soc-google' => 'Social Google',
        'soc-mailru' => 'Social Mailru',
        'soc-ok' => 'Social OK',
        'soc-vk' => 'Social VK',
        'soc-yandex' => 'Social Yandex',
        'soc-other' => 'Social Other',
        'tax-number' => 'Tax number',

        'special-wmid' => 'WMID',
        'special-hidden' => 'Hidden',

        'attr-vip' => 'Attribute assigned to VIP users',
        'attr-contacted' => 'Attribute assigned to user who has been contacted',
        'attr-blocked' => 'Attribute assigned to blocked user and requisite',
        'attr-chargeback' => 'Attribute assigned to requisite which has charge back transaction',
        'attr-prev-blocked' => 'Attribute assigned to requisite which has been previously blocked',
        'attr-test' => 'Attribute assigned to test user',
        'attr-verified' => 'Attribute assigned to user who has verified documents',
        'attr-bonus-black-list' => 'Attribute assigned to user which has been added to bonus blacklist',

        'multi-source-cluster' => 'Multi Source Cluster. Expand only in "Clusters" mode: Ctrl+click - expand to clusters, +Shift - expand to nodes, +Alt - chain expand, Esc - stop chain expand',
        'cluster-email' => 'Users Cluster by Email. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-req-cc' => 'Users Cluster by Requisite Credit Card. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-req-qiwi' => 'Users Cluster by Requisite Qiwi. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-req-webmoney' => 'Users Cluster by Requisite Webmoney. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-req-other' => 'Users Cluster by Requisite other. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-soc-facebook' => 'Users Cluster by Social Facebook. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-soc-google' => 'Users Cluster by Social Google. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-soc-mailru' => 'Users Cluster by Social Mailru. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-soc-ok' => 'Users Cluster by Social OK. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-soc-vk' => 'Users Cluster by Social VK. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-soc-yandex' => 'Users Cluster by Social Yande. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-soc-other' => 'Users Cluster by Social Other. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-special-wmid' => 'Users Cluster by WMID. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-special-hidden' => 'Users Cluster by Hidden. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-id-card' => 'Users Cluster by Recognized similar faces from documents. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
        'cluster-tax-number' => 'Users Cluster by tax number. Ctrl+Click - expand, +Alt-chain, Esc - stop chain expand',
    ];

    protected string $iconPath = '/images/cid';

    public function getImagePath(string $alias): string
    {
        return $this->iconPath . "/" . static::ICONS[$alias];
    }

    public function getPreloadImages(): array
    {
        $result = [];
        foreach (static::ICONS as $alias => $icon) {
            $result[$alias] = $this->getImagePath($alias);
        }
        return $result;
    }

    public function getImagesDescriptions(): array
    {
        $result = [];
        foreach (static::ICONS_DESCRIPTION as $alias => $description) {
            $result[] = [
                'path' => $this->getImagePath($alias),
                'description' => $description,
            ];
        }
        return $result;
    }

    public function getImagesTypes(): array
    {
        return array_map(fn($alias) => $this->getImagePath($alias), self::ICONS_TYPES);
    }
}
