<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\wp3;

use app\back\components\validators\BooleanValidator;
use app\back\modules\api\components\Operators;
use app\back\repositories\AffData;
use app\back\repositories\Refcodes;
use app\back\repositories\Useragents;
use app\back\repositories\UserLogins;
use app\back\repositories\Users;
use Yiisoft\Db\Query\Query;

class LoginsMethod extends BaseWp3GetMethod
{
    #[Operators(Operators::EQ)]
    #[BooleanValidator]
    protected array $is_logins_refcode = [];

    public function run(): iterable
    {
        $request = $this->createRequest();

        $isLoginsRefcode = $request->getParam('is_logins_refcode', false, true);
        $aliasForJoiningRefcode = $isLoginsRefcode ? 'ul' : 'u';

        [$dateFrom, $dateTo] = $this->getDates($request);

        $request->map([
            'site_id' => 'ul.site_id',
            'brand_id' => 'u.brand_id',
            'traffic_source' => 'r.ts_id',
        ]);

        $select = [
            'ref' => "MIN(r.code)",
            'user_id' => 'ul.user_id',
            'count' => 'COUNT(*)',
            'ip' => 'ul.ip',
            'success' => 'MAX(CASE WHEN(ul.success = true) THEN 1 ELSE 0 END)',
            'user_agent' => 'MAX(uag.name)',
            'aff_data' => 'MAX(ad.aff_data)',
        ];

        $query = (new Query($this->db))
            ->select($select)
            ->from(['ul' => UserLogins::TABLE_NAME])
            ->leftJoin(['uag' => Useragents::TABLE_NAME], 'uag.id = ul.useragent_id')
            ->leftJoin(['u' => Users::TABLE_NAME], 'u.site_id = ul.site_id AND u.user_id = ul.user_id')
            ->leftJoin(['r' => Refcodes::TABLE_NAME], "r.id = $aliasForJoiningRefcode.refcode_id")
            ->leftJoin(['ad' => AffData::TABLE_NAME], 'ad.id = u.aff_data_id')
            ->where([
                'AND',
                ['>=', 'ul.date', $dateFrom],
                ['<', 'ul.date', $dateTo],
            ])
            ->groupBy(['ul.user_id', 'ul.ip']);

        $request->filterParams($query);

        return $this->fetchEach($query);
    }
}
