<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\marketing;

use app\back\entities\User;
use app\back\entities\UserMetric;
use app\back\entities\UserTransaction;
use app\back\modules\api\params\ApiParamDateHour;
use app\back\modules\api\params\ApiParamSiteIdSecured;
use app\back\repositories\Games;
use app\back\repositories\LoyaltyStatuses;
use app\back\repositories\Refcodes;
use app\back\repositories\SocialNets;
use app\back\repositories\UseragentApps;
use app\back\repositories\UseragentPlatforms;
use app\back\repositories\Useragents;
use app\back\repositories\UserLogins;
use app\back\repositories\UserLoyalties;
use app\back\repositories\UserMetrics;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UsersActionsMethod extends BaseMarketingGetMethod
{
    use ApiParamSiteIdSecured;
    use ApiParamDateHour;

    public function run(): iterable
    {
        $request = $this->createRequest();

        [$dateFrom, $dateTo, $siteId] = $this->getDatesAndSite($request);

        $loginUsers = (new Query($this->db))
            ->select([
                'site_id',
                'user_id',
                'login_refcode_id' => 'MAX(ua.refcode_id)'
            ])
            ->from(['ua' => UserLogins::TABLE_NAME])
            ->where([
                'AND',
                ['>=', 'date', $dateFrom],
                ['<', 'date', $dateTo],
                ['site_id' => $siteId],
            ])
            ->groupBy(['site_id', 'user_id']);

        $depUsers = (new Query($this->db))
            ->select([
                'site_id',
                'user_id',
                'login_refcode_id' => new Expression('null'),
            ])
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->where([
                'AND',
                ['>=', 'us.updated_at', $dateFrom],
                ['<', 'us.updated_at', $dateTo],
                [
                    'site_id' => $siteId,
                    'status' => UserTransaction::STATUS_SUCCESS,
                    'ext_type' => UserTransaction::EXT_TYPE_NORMAL,
                    'dir' => UserTransaction::DIR_IN,
                ],
            ])
            ->groupBy(['site_id', 'user_id']);

        [$favGameMetricId, $favGameId] = UserMetric::M_FAV_GAME_LT_1;

        $query = (new Query($this->db))
            ->select([
                'product_uid' => 'u.user_id',
                'cid' => 'u.cid',
                'last_login' => 'usi.login_last_at',
                'last_deposit' => 'usi.dep_last_at',
                'deposits_sum' => 'usi.dep_lt_usd',
                'deposits_count' => 'usi.dep_lt_count',
                'withdrawals_sum' => 'usi.wd_lt_usd',
                'withdrawals_count' => 'usi.wd_lt_count',
                'favourite_game' => 'g.name',
                'refcode_registration' => "r.code",
                'refcode_login' => 'rl.code',
                'country' => 'u.country',
                'platform' => self::REPLACED_PLATFORM,
                'social_net' => 's.social_name',
                'social_key' => 'u.social_key',
                'loyalty_status' => 'ls.title',
                'full_status' => User::getFullStatusExpression(),
                'registration_date' => 'u.date',
            ])
            ->from(['au' => $loginUsers->union($depUsers)])
            ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = au.site_id AND u.user_id = au.user_id')
            ->leftJoin(['s' => SocialNets::TABLE_NAME], 's.social_id = u.social_id')
            ->leftJoin(['usi' => UserSpecialInfos::TABLE_NAME], 'usi.site_id = u.site_id AND usi.user_id = u.user_id')
            ->leftJoin(['um_fg' => UserMetrics::TABLE_NAME], 'um_fg.site_id = u.site_id AND um_fg.user_id = u.user_id AND um_fg.metric = :fav_game_metric_id', ['fav_game_metric_id' => $favGameMetricId])
            ->leftJoin(['g' => Games::TABLE_NAME], "g.id = um_fg.$favGameId")
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = u.refcode_id')
            ->leftJoin(['rl' => Refcodes::TABLE_NAME], 'rl.id = au.login_refcode_id')
            ->leftJoin(['ul' => UserLoyalties::TABLE_NAME], 'ul.site_id = u.site_id AND ul.user_id = u.user_id')
            ->leftJoin(['ls' => LoyaltyStatuses::TABLE_NAME], 'ls.site_id = ul.site_id AND ls.id = ul.status_id')
            ->leftJoin(['uag' => Useragents::TABLE_NAME], 'u.useragent_id = uag.id')
            ->leftJoin(['uagp' => UseragentPlatforms::TABLE_NAME], 'uag.platform_id = uagp.id')
            ->leftJoin(['uaga' => UseragentApps::TABLE_NAME], 'uaga.id = uag.app_id');

        $request->filterParams($query);

        return $this->fetchEach($query);
    }
}
