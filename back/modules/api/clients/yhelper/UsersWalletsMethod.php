<?php

declare(strict_types=1);

namespace app\back\modules\api\clients\yhelper;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\SiteIdUserIdValidator;
use app\back\entities\UserWallet;
use app\back\modules\api\ApiGetMethod;
use app\back\modules\api\components\Operators;
use app\back\repositories\UserWallets;
use Yiisoft\Db\Query\Query;

class UsersWalletsMethod extends ApiGetMethod
{
    #[Operators(Operators::EQ)]
    #[IdValidator]
    protected array $site_id = [];
    #[Operators(Operators::EQ_IN)]
    #[BigIdValidator]
    protected array $user_id = [];
    #[Operators(Operators::IN)]
    #[SiteIdUserIdValidator]
    protected array $site_user = [];

    public function run(): iterable
    {
        $request = $this->createRequest();
        $query = (new Query($this->db))
            ->select([
                'siteId' => 'w.site_id',
                'userId' => 'w.user_id',
                'type' => '(CASE w.type WHEN :real THEN :realAlias WHEN :bonus THEN :bonusAlias END)',
                'balance'     => 'COALESCE(SUM(w.balance) FILTER (WHERE ' . UserWallets::getActiveCondition('w') . '), 0)',
                'balanceReal' => 'COALESCE(SUM(w.balance) FILTER (WHERE ' . UserWallets::getActiveCondition('w') . '), 0)',
            ])
            ->from(['w' => UserWallets::TABLE_NAME])
            ->addParams([
                'real' => UserWallet::TYPE_REAL,
                'bonus' => UserWallet::TYPE_BONUS,
                'realAlias' => UserWallet::TYPES[UserWallet::TYPE_REAL],
                'bonusAlias' => UserWallet::TYPES[UserWallet::TYPE_BONUS],
            ])
            ->groupBy(['w.site_id', 'w.user_id', 'w.type']);

        $request->filterSiteUser($this->db, $query, 'w');

        $request->filterParams($query);

        return $this->fetchEach($query);
    }
}
