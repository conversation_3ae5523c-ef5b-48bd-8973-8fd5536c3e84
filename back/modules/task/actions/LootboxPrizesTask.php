<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\entities\LootboxPrize;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\LootboxPrizes;

class LootboxPrizesTask extends ImportTask
{
    use TaskWithDefaultGetData;

    private const array PRIZE_TYPE_MAP = [
        'money' => LootboxPrize::TYPE_MONEY,
        'gift_spins' => LootboxPrize::TYPE_GIFT_SPINS,
        'x2' => LootboxPrize::TYPE_X2,
        'x3' => LootboxPrize::TYPE_X3,
        'wheel_fortune_spin' => LootboxPrize::TYPE_WHEEL_FORTUNE_SPINS,
        'blank' => LootboxPrize::TYPE_BLANK,
        'cashback' => LootboxPrize::TYPE_CASHBACK,
        'free_spins' => LootboxPrize::TYPE_FREE_SPINS,
        'lottery_tickets' => LootboxPrize::TYPE_LOTTERY,
        'loyalty_status' => LootboxPrize::TYPE_LOYALTY_STATUS,
        'points' => LootboxPrize::TYPE_LOYALTY_POINTS,
        'protection' => LootboxPrize::TYPE_PROTECTION,
        'talisman' => LootboxPrize::TYPE_TALISMAN,
        'wheel_fortune_register_spin' => LootboxPrize::TYPE_WHEEL_FORTUNE_REGISTER_SPINS,
        'birthday_prize' => LootboxPrize::TYPE_BIRTHDAY,
        'free_spins_kanggiten' => LootboxPrize::TYPE_FREE_SPINS,
        'free_spins_uranus' => LootboxPrize::TYPE_FREE_SPINS,
    ];

    public function __construct(
        private readonly LootboxPrizes $lootboxPrizesRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
    ) {
    }

    protected function repository(): LootboxPrizes
    {
        return $this->lootboxPrizesRepo;
    }

    protected function beforeFind(array &$row): bool
    {
        $row['site_id'] = $this->siteIdResolver->siteId();

        if (!array_key_exists($row['type'], static::PRIZE_TYPE_MAP)) {
            $this->log->error("Unknown type: {$row['type']}");
            return false;
        }

        $row['type'] = static::PRIZE_TYPE_MAP[$row['type']];

        return true;
    }
}
