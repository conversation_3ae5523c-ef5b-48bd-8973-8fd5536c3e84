<?php

declare(strict_types=1);

namespace app\back\modules\task\actions;

use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Json;
use app\back\entities\UserTransaction;
use app\back\modules\task\CurrencyConversionTrait;
use app\back\modules\task\ImportTask;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\BaseRepository;
use app\back\repositories\Rates;
use app\back\repositories\UserTransactions;
use Yiisoft\Db\Expression\Expression;

class UsersStatsBonusesSmenTask extends ImportTask
{
    use TaskWithFromToRequest;
    use CurrencyConversionTrait;

    private const string BONUS_ID_PREFIX = 'b';

    /** @var string Transaction types */
    private const string TT_CREATED = 'created';
    private const string TT_RESET_BB = 'reset_bonus_balance';
    private const string TT_EXPIRED_BB = 'balance_was_expired';

    public string $dateFormat = DateHelper::DATETIME_FORMAT_PHP;

    public function __construct(
        private readonly UserTransactions $userTransactionsRepo,
        private readonly TaskSiteIdResolver $siteIdResolver,
        private readonly Rates $ratesRepo,
    ) {
    }

    private const array SOURCE_NAMES_TO_OP_IDS = [
        'bonus_reward' => UserTransaction::OP_BONUS_BALANCE_PRIZE,
        'wof_prize' => UserTransaction::OP_WHEEL_FORTUNE_PRIZE,
        'fee_bonus' => UserTransaction::OP_PAY_SYSTEM_BONUS,
        'strip_bonus' => UserTransaction::OP_STRIP_BONUS,
        'kanggiten_reward' => UserTransaction::OP_GAME_PROVIDER_BONUS,
        'stash_bonus' => UserTransaction::OP_STASH_BONUS,
        'bet_stash_bonus' => UserTransaction::OP_ACTIVE_PLAYER_BONUS,
        'payment' => UserTransaction::OP_BONUS_BALANCE_PRIZE, // Legacy, not all transferred to new bonus system, fallback. Remove after @diesel approve
        'loot_box_prize' => UserTransaction::OP_LOOTBOX_PRIZE,
        'referral_system' => UserTransaction::OP_REFERRAL_PROGRAM,
        'active_player' => UserTransaction::OP_ACTIVE_PLAYER_BONUS,
        'tournament' => UserTransaction::OP_TOURNAMENT_BONUS,
        'fun_tournament' => UserTransaction::OP_TOURNAMENT_FUN_BONUS,
        'hhs_white_reward' => UserTransaction::OP_GAME_PROVIDER_BONUS,
    ];

    private const array TRANSACTION_TYPE_TO_OP_IDS = [
        self::TT_RESET_BB => UserTransaction::OP_RESET_BONUS_BALANCE,
        self::TT_EXPIRED_BB => UserTransaction::OP_EXPIRE_BONUS_BALANCE,
    ];

    protected function beforeFind(array &$row): bool
    {
        if ($row['created_at'] < '2024-07-16') { // Remove after SMEN filtered users-stats api method
            return false;
        }

        $row['site_id'] = $this->siteIdResolver->siteId();
        $row['transaction_id'] = static::BONUS_ID_PREFIX . $row['transaction_id'];
        $row['balance_type'] = UserTransaction::BALANCE_TYPE_BONUS;

        $metadata = Json::decode($row['metadata']);
        unset($row['metadata']);

        if (empty($row['transaction_type']) || $row['transaction_type'] === self::TT_CREATED) {
            if (!empty($row['source_name'])) {
                if (empty(self::SOURCE_NAMES_TO_OP_IDS[$row['source_name']])) {
                    $this->log->error('Unknown source: ' . $row['source_name']);
                    return false;
                }
                $row['op_id'] = self::SOURCE_NAMES_TO_OP_IDS[$row['source_name']];
            }
        } else {
            /**
             * Use $metadata['type'] instead of $row['transaction_type']
             * because of reset_bonus_balance separate on two types "balance_was_expired"|"reset_bonus_balance"
             */
            if (empty(self::TRANSACTION_TYPE_TO_OP_IDS[$metadata['type']])) {
                $this->log->error('Unknown transaction_type: ' . $metadata['type']);
                return false;
            }
            $row['op_id'] = self::TRANSACTION_TYPE_TO_OP_IDS[$metadata['type']];
        }

        if ($row['op_id'] === UserTransaction::OP_RESET_BONUS_BALANCE && isset($metadata['reset_bonus_balance_reason']) && $metadata['reset_bonus_balance_reason'] === 'reset_before_new_bonus_add') {
            return false; // Skip technical bonus reset just before new bonus creation for not multibalances products
        }

        $this->convertCurrencyAmounts($row);

        if (is_numeric($row['comment_admin'])) {
            $row['bonus_id'] = $row['comment_admin'];
            $row['comment_admin'] = null;
        }

        return true;
    }

    protected function repository(): BaseRepository
    {
        return $this->userTransactionsRepo;
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        return $repository->batchUpsert($rows, [
            'upserted_at' => new Expression('now()'),
        ]);
    }
}
