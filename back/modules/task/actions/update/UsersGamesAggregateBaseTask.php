<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\entities\UserGameRaw;
use app\back\entities\UserGameToken;
use app\back\entities\UserLyraActivity;
use app\back\modules\task\BaseTask;
use app\back\modules\task\components\UsersLyraActivityTrait;
use app\back\modules\task\TaskSiteIdResolver;
use app\back\repositories\Rates;
use app\back\repositories\UserGameRaws;
use app\back\repositories\UserGameTokens;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Query\Query;

class UsersGamesAggregateBaseTask extends BaseTask
{
    use UsersLyraActivityTrait;

    private const int BATCH_SIZE = 100;

    public function __construct(
        protected readonly ConnectionInterface $db,
        protected readonly TaskSiteIdResolver $siteIdResolver,
        protected UserGameTokens $userGameTokensRepo,
        private readonly Rates $ratesRepo,
    ) {
    }

    public function process(): void
    {
        $sessions = $this->getSessions();

        $this->log->debug('Fetching records');

        foreach ($sessions->batch(self::BATCH_SIZE) as $i => $batch) {
            if ($i === 0) {
                $this->log->debug('First fetched');
            }

            $data = [];

            foreach ($batch as $session) {
                $this->addData($session, $data);
            }

            $this->log->debug('importing ' . count($data) . ' record(s)');
            $this->totalRows += count($data);
            $this->affectedRows += $this->userGameTokensRepo->batchUpsert($data, ['updated_at' => new \DateTimeImmutable(UserGameToken::SQL_NOW_DATETIME)]);

            if (!$this->skipEvents) {
                $rowForLyraActivity = array_map(static fn($row) => [
                    'site_id' => $row['site_id'],
                    'user_id' => $row['user_id'],
                    'date' => $row['last_action_at']
                ], $data);

                $this->updateLyraUsersActivity(UserLyraActivity::SOURCE_GAMES, $rowForLyraActivity);
            }
        }
    }

    protected function getSessions(): Query
    {
        return (new Query($this->db))
            ->select([
                'ugr.site_id',
                'ugr.session_token',
                'round_types' => "JSON_AGG(DISTINCT COALESCE(ugr.round_type, :paid))",
                'auth_token' => 'MIN(ugr.auth_token)',
                'user_id' => 'MIN(ugr.user_id)',
                'game_id' => 'MIN(ugr.game_id)',
                'currency' => 'MIN(ugr.currency)',
            ])
            ->from(['ugr' => UserGameRaws::TABLE_NAME])
            ->where([
                'AND',
                ['ugr.site_id' => $this->siteIdResolver->siteId()],
                ['>=', 'ugr.created_at', $this->from],
                ['<', 'ugr.created_at', $this->to],
            ])
            ->groupBy([
                'ugr.site_id',
                'ugr.session_token',
            ])
            ->addParams([
                'paid' => UserGameRaw::ROUND_TYPE_PAID,
            ]);
    }

    protected function addData(array $session, array &$data): void
    {
        if (empty($session['game_id'])) {
            $this->log->error("Session has no game_id");
        }

        $sessionAggregatesByRoundType = $this->getSessionAggregatesByRoundType($session);
        $this->appendSessionAuthInfo($session);

        if (isset($sessionAggregatesByRoundType[UserGameRaw::ROUND_TYPE_FREE])) {
            $this->addSessionType(UserGameToken::SESSION_TYPE_GIFTSPINS, $data, $session, $sessionAggregatesByRoundType[UserGameRaw::ROUND_TYPE_FREE]);
            unset($sessionAggregatesByRoundType[UserGameRaw::ROUND_TYPE_FREE]);
        }

        if (isset($sessionAggregatesByRoundType[UserGameRaw::ROUND_TYPE_PAID])) {
            $this->addSessionType(UserGameToken::SESSION_TYPE_PAIDSPINS, $data, $session, $sessionAggregatesByRoundType[UserGameRaw::ROUND_TYPE_PAID]);
            unset($sessionAggregatesByRoundType[UserGameRaw::ROUND_TYPE_PAID]);
        }

        if (!empty($sessionAggregatesByRoundType)) {
            $this->log->error("Session has unknown round types: " . Json::encode(array_keys($sessionAggregatesByRoundType)));
        }
    }

    protected function getSessionAggregatesByRoundType(array $session): array
    {
        $roundTypeExpression = 'COALESCE(ugr.round_type, :paid)';

        $query = (new Query($this->db))
            ->select([
                'round_type' => $roundTypeExpression,
                'created_at' => 'MIN(ugr.created_at)',
                'last_action_at' => 'MAX(ugr.created_at)',
                'bet_count_real' => "COUNT(*) FILTER (WHERE type = 'bet' AND real_amount > 0)",
                'win_count_real' => "COUNT(*) FILTER (WHERE type = 'win' AND real_amount > 0)",
                'bet_count_bonus' => "COUNT(*) FILTER (WHERE type = 'bet' AND bonus_amount > 0)",
                'win_count_bonus' => "COUNT(*) FILTER (WHERE type = 'win' AND bonus_amount > 0)",

                'bet_amount_real' => "SUM(real_amount) FILTER (WHERE type = 'bet') - COALESCE(SUM(real_amount) FILTER (WHERE type = 'refund'), 0)",
                'win_amount_real' => "SUM(real_amount) FILTER (WHERE type = 'win')",
                'bet_amount_bonus' => "SUM(bonus_amount) FILTER (WHERE type = 'bet') - COALESCE(SUM(bonus_amount) FILTER (WHERE type = 'refund'), 0)",
                'win_amount_bonus' => "SUM(bonus_amount) FILTER (WHERE type = 'win')",

                'after_amount_real' => '(ARRAY_AGG(ugr.real_amount_after ORDER BY ugr.created_at DESC))[1]',
                'after_amount_bonus' => '(ARRAY_AGG(ugr.bonus_amount_after ORDER BY ugr.created_at DESC))[1]',
                'win_max' => "MAX(real_amount + bonus_amount) FILTER (WHERE type = 'win')",
            ])
            ->from(['ugr' => UserGameRaws::TABLE_NAME])
            ->where([
                'ugr.site_id' => $session['site_id'],
                'ugr.session_token' => $session['session_token'],
            ])
            ->groupBy([
                $roundTypeExpression,
            ])
            ->addParams([
                'paid' => UserGameRaw::ROUND_TYPE_PAID,
            ])
            ->indexBy('round_type');

        return $query->all();
    }

    protected function appendSessionAuthInfo(array &$session): void
    {
    }

    protected function addSessionType(int $sessionType, array &$data, array $session, array $aggregates): void
    {
        $row = [
            'site_id' => $session['site_id'],
            'token_id' => $session['session_token'],
            'user_id' => $session['user_id'],
            'game_id' => $session['game_id'],
            'currency' => $session['currency'],

            'created_at' => $aggregates['created_at'],
            'last_action_at' => $aggregates['last_action_at'],
            'win_max' => $aggregates['win_max'],

            'session_type' => $sessionType,
            'useragent_id' => $session['useragent_id'] ?? null,
            'ip' => $session['ip'] ?? null,
            'refcode_id' => $session['refcode_id'] ?? null,
            'host_id' => $session['host_id'] ?? null,
        ];

        if ($aggregates['bet_count_real'] > 0 || $aggregates['win_count_real'] > 0) {
            $this->addRow(array_merge($row, [
                'bet_count' => $aggregates['bet_count_real'],
                'win_count' => $aggregates['win_count_real'],
                'bet_amount' => $aggregates['bet_amount_real'] ?? 0,
                'win_amount' => $aggregates['win_amount_real'] ?? 0,
                'after_amount' => $aggregates['after_amount_real'] ?? 0,
                'balance_type' => UserGameToken::BALANCE_TYPE_REAL,
            ]), $data);
        }

        if ($aggregates['bet_count_bonus'] > 0 || $aggregates['win_count_bonus'] > 0) {
            $this->addRow(array_merge($row, [
                'bet_count' => $aggregates['bet_count_bonus'],
                'win_count' => $aggregates['win_count_bonus'],
                'bet_amount' => $aggregates['bet_amount_bonus'] ?? 0,
                'win_amount' => $aggregates['win_amount_bonus'] ?? 0,
                'after_amount' => $aggregates['after_amount_bonus'] ?? 0,
                'balance_type' => UserGameToken::BALANCE_TYPE_BONUS,
            ]), $data);
        }
    }

    private function addRow(array $row, array &$data): void
    {
        foreach (['bet', 'win', 'after'] as $a) {
            foreach (['usd', 'eur'] as $c) {
                $row["{$a}_amount_{$c}"] = $this->ratesRepo->convert($row["{$a}_amount"], $row['currency'], mb_strtoupper($c), $row['created_at']);
            }
        }

        $data[] = $row;
    }
}
