<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\update;

use app\back\components\helpers\Db;
use app\back\modules\task\BaseTask;
use app\back\repositories\Refcodes;
use app\back\repositories\Sites;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class RefcodesSourceSiteTask extends BaseTask
{
    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Sites $sitesRepo
    ) {
    }

    // Working with RM refcodes
    public function process(): void
    {
        $rmRules = array_filter($this->sitesRepo->sourceCodes());

        foreach ($rmRules as $siteId => $refs) {
            $where = [
                'OR',
            ];

            foreach (array_filter($refs) as $r) {
                $where[] = ['LIKE', 'code', Db::escapeLikeVal($r) . '%', null];
            }

            $where = [
                'AND',
                $where,
                ['IS DISTINCT FROM', 'source_site_id', $siteId]
            ];

            $refcodes = (new Query($this->db))
                ->select(['id'])
                ->from([Refcodes::TABLE_NAME])
                ->where($where);

            foreach ($refcodes->batch(1000) as $ids) {
                $this->affectedRows += $this->db->createCommand()->update(Refcodes::TABLE_NAME, [
                    'source_site_id' => $siteId,
                    'updated_at' => new Expression('NOW()'),
                ], [
                    'id' => $ids,
                ])->execute();
            }
        }

        $this->totalRows = $this->affectedRows;
    }
}
