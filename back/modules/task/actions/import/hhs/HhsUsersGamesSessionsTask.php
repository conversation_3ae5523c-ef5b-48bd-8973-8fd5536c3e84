<?php

declare(strict_types=1);

namespace app\back\modules\task\actions\import\hhs;

use app\back\components\helpers\Arr;
use app\back\entities\HhsUserGameSession;
use app\back\entities\Rate;
use app\back\modules\task\actions\TaskWithSplitFromToRequest;
use app\back\repositories\BaseRepository;
use app\back\repositories\HhsProjects;
use app\back\repositories\HhsUserGameSessions;
use app\back\repositories\Rates;

class HhsUsersGamesSessionsTask extends HhsBaseTask
{
    use TaskWithSplitFromToRequest;

    private const array TAGS = [
        '' => HhsUserGameSession::TAG_PAID,
        '::test_user' => HhsUserGameSession::TAG_TEST,
        '::bonus_spins' => HhsUserGameSession::TAG_BONUS_SPINS,
        '::freespins_on_start' => HhsUserGameSession::TAG_FREE_SPINS,
    ];

    private const array FROM_CENTS = [
        'bet_sum',
        'win_sum',
        'bet_sum_usd',
        'win_sum_usd',
        'double_bet_sum',
        'double_win_sum',
        'double_bet_sum_usd',
        'double_win_sum_usd',
    ];

    private array $projectsWithNonNumericUserIds = [];

    public function __construct(
        private readonly HhsUserGameSessions $hhsUserGameSessionsRepo,
        private readonly Rates $ratesRepo,
        private readonly HhsProjects $hhsProjectsRepo,
    ) {
    }

    protected function getData(): iterable
    {
        return $this->getSplitData();
    }

    protected function requestConfig(): array
    {
        return ['mode' => 'REAL'];
    }

    protected function beforeFind(array &$row): bool
    {
        if (empty($row['user_id_raw'])) { // Skip sessions without user
            return false;
        }

        if (ctype_digit($row['user_id_raw']) && $row['user_id_raw'] < PHP_INT_MAX && $row['user_id_raw'] > 0) {
            $row['user_id'] = (int)$row['user_id_raw'];
        }

        if (empty($row['user_id']) && !isset($this->projectsWithNonNumericUserIds[$row['project_id']])) {
            $this->projectsWithNonNumericUserIds[$row['project_id']] = true;
            //$this->log->notice("Empty user id in project {$row['project_id']}");

            return false;
        }

        $row['tag_id'] = self::TAGS[$row['tag_name']] ?? null;
        if ($row['tag_id'] === null) {
            $this->log->notice("Unknown tag_name: {$row['tag_name']}");
            return false;
        }
        unset($row['tag_name']);

        $row['game_id'] += $this->gameIdIncrement;
        $row['project_id'] += $this->projectIdIncrement;
        $row['instance_id'] = $this->instanceId;

        $row['site_id'] = $this->hhsProjectsRepo->getSiteIdByHhsProjectId($row['project_id']);


        foreach (self::FROM_CENTS as $fc) {
            $row[$fc] /= 100;
        }

        $row['currency'] = Rate::fixCurrency($row['currency']);

        if (!$this->ratesRepo->exist($row['currency'])) {
            //$this->log->notice("Unknown rate {$row['currency']}");
            $row['bet_sum'] = $row['bet_sum_usd'];
            $row['win_sum'] = $row['win_sum_usd'];
            $row['double_bet_sum'] = $row['double_bet_sum_usd'];
            $row['double_win_sum'] = $row['double_win_sum_usd'];
            $row['currency'] = Rate::USD;
        }

        $row['bet_sum_rub'] = $this->ratesRepo->convert($row['bet_sum'], $row['currency'], Rate::RUB, $row['created_at']);
        $row['win_sum_rub'] = $this->ratesRepo->convert($row['win_sum'], $row['currency'], Rate::RUB, $row['created_at']);
        $row['double_bet_sum_rub'] = $this->ratesRepo->convert($row['double_bet_sum'], $row['currency'], Rate::RUB, $row['created_at']);
        $row['double_win_sum_rub'] = $this->ratesRepo->convert($row['double_win_sum'], $row['currency'], Rate::RUB, $row['created_at']);

        $row['bet_sum_eur'] = $this->ratesRepo->convert($row['bet_sum'], $row['currency'], Rate::EUR, $row['created_at']);
        $row['win_sum_eur'] = $this->ratesRepo->convert($row['win_sum'], $row['currency'], Rate::EUR, $row['created_at']);
        $row['double_bet_sum_eur'] = $this->ratesRepo->convert($row['double_bet_sum'], $row['currency'], Rate::EUR, $row['created_at']);
        $row['double_win_sum_eur'] = $this->ratesRepo->convert($row['double_win_sum'], $row['currency'], Rate::EUR, $row['created_at']);

        return parent::beforeFind($row);
    }

    protected function batchUpsert(BaseRepository $repository, array $rows): int
    {
        Arr::removeDuplicatesByColumns($rows, ['date', 'session_id', 'tag_id'], 'updated_at');
        return $repository->batchUpsert($rows, [], '(date, session_id, tag_id)');
    }

    protected function repository(): BaseRepository
    {
        return $this->hhsUserGameSessionsRepo;
    }
}
