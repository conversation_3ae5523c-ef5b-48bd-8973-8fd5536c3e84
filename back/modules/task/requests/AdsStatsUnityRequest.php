<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\parsers\CsvParser;
use app\back\entities\AdCredential;
use app\back\entities\Proxy;
use app\back\repositories\AdCredentials;
use Symfony\Contracts\HttpClient\Exception\TransportExceptionInterface;

// https://unityads.unity3d.com/help/advertising/stats-api
class AdsStatsUnityRequest extends BaseRequest
{
    use RequestWithHttpClient;
    use RequestWithParserAndConverter;
    use AdsCredentialsInvalidateTrait;

    public AdCredentials $adCredentialsRepo;
    public array $credentials;
    public string $from;
    public string $to;

    protected function fetchData(): iterable
    {
        $dateFormat = 'Y-m-d\TH:i:s';
        $start = date($dateFormat, strtotime($this->from));
        $end = date($dateFormat, strtotime($this->to));

        foreach ($this->credentials as ['credential' => $credential, 'proxy' => $proxy]) {
            /** @var AdCredential $credential */
            /** @var Proxy $proxy */
            $requestCredentials = $credential->getValueObject();

            $client = $this->createHttpClient([
                'proxy' => $proxy?->config,
                'auth_basic' => "$requestCredentials->key:$requestCredentials->secret",
            ]);

            $url = $this->buildUrl([
                ':organization' => $requestCredentials->organization,
                ':start' => $start,
                ':end' => $end,
            ]);

            try {
                $data = $this->responseToData($client->get($url));
            } catch (TransportExceptionInterface $e) {
                $this->invalidateProxyOnError($e, $proxy, $credential);
                continue;
            }

            sleep(3); //{"message":"Request rate limit exceeded. You are allowed to make 1 number of request(s) every 3 second(s)."}

            foreach ($data as $datum) {
                yield $datum;
            }

            $this->adCredentialsRepo->updateLastLoad($credential);
        }
    }

    protected function parserConfig(): string|array
    {
        return CsvParser::class;
    }
}
