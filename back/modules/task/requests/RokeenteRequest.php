<?php

declare(strict_types=1);

namespace app\back\modules\task\requests;

use app\back\components\parsers\CsvParser;

class RokeenteRequest extends BaseRequest
{
    use RequestWithHttpClient;
    use RequestWithParserAndConverter;

    public string $authKey;
    public string $from;
    public string $to;

    protected function fetchData(): iterable
    {
        $client = $this->createHttpClient();

        $url = $this->buildUrl();

        $response = $client->post($url, [
            'api_token' =>  $this->authKey,
            'fields' => implode(',', $this->aliases),
            'date_from' => date('Y-m-d H:i:s', strtotime($this->from)),
            'date_to' => date('Y-m-d H:i:s', strtotime($this->to)),
        ]);

        return $this->responseToData($response);
    }

    protected function parserConfig(): string|array
    {
        return [
            'class' => CsvParser::class,
            'escape' => '',
        ];
    }
}
