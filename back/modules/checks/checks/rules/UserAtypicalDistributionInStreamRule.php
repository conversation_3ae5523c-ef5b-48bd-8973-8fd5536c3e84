<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\rules;

use app\back\components\exceptions\InvalidException;
use app\back\components\helpers\Arr;
use app\back\components\InBuilder;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\CallableValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\entities\LyraStreamAtypicalDistributionStat;
use app\back\repositories\Cities;
use app\back\repositories\LyraStreamAtypicalDistributionStats;
use app\back\repositories\Refcodes;
use app\back\repositories\RokeenteSessions;
use app\back\repositories\RokeenteSessionUsers;
use app\back\repositories\UseragentDevices;
use app\back\repositories\Useragents;
use app\back\repositories\Users;
use app\back\repositories\UserSpecialInfos;
use Yiisoft\Db\Connection\ConnectionInterface;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class UserAtypicalDistributionInStreamRule extends BaseSiteUserRule
{
    #[IntInArrayValidator(LyraStreamAtypicalDistributionStat::ALLOWED_DISTRIBUTION_TYPES)]
    public int $distributionType;
    #[StringMultilineValidator(1)]
    #[CallableValidator([self::class, 'validateRulesMap'])]
    public ?string $rulesMap = null;
    #[IntValidator(1)]
    public ?int $period = null;
    #[IntValidator]
    public int $streamSize = 0;
    #[StringInArrayValidator(self::OPERATORS)]
    public string $streamSizeOperator = self::OPERATOR_GREATER_OR_EQ;

    public ?array $rulesMapArray = null;

    protected function getQuery(ConnectionInterface $db, InBuilder $inBuilder): Query
    {
        $inBuilder->setMap(['intval', 'intval']);

        $distributionTableNameAlias = "lsads";

        $cond = [];
        $expParams = [];
        $i = 0;
        foreach ($this->rulesMapToArray() as $steamUsersCountSize => $positiveCasePercent) {
            $streamSizeKey = "stream_size_exp_{$i}";
            $streamCaseKey = "stream_case_exp_{$i}";
            $cond[] = "{$distributionTableNameAlias}.entity_stream_size >= :$streamSizeKey THEN {$distributionTableNameAlias}.entity_stream_percent >= :$streamCaseKey";
            $expParams[$streamSizeKey] = $steamUsersCountSize;
            $expParams[$streamCaseKey] = $positiveCasePercent;
            $i++;
        }

        $q = (new Query($db))
            ->select([
                static::RESULT_COL_RESULT => new Expression("CASE WHEN " . implode(' WHEN ', $cond) . ' ELSE FALSE END', $expParams),
                static::RESULT_COL_PK => SiteUserBuilder::siteUserQueryExpression('f'),
            ])
            ->from($inBuilder->table('f', ['site_id', 'user_id']))
            ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = f.site_id and u.user_id = f.user_id')
            ->leftJoin(['r' => Refcodes::TABLE_NAME], 'r.id = u.refcode_id and r.webmaster_id is not null');

        return $this->decorateQueryByDistributionType($q, $distributionTableNameAlias);
    }

    private function decorateQueryByDistributionType(Query $query, string $distributionTableNameAlias = 'lsads'): Query
    {
        $f = match ($this->distributionType) {
            LyraStreamAtypicalDistributionStat::ENTITY_TYPE_UA_DEVICE_DISTRIBUTION => function (Query $query) use ($distributionTableNameAlias) {
                $query->addSelect([static::RESULT_COL_INFO => new Expression("ua_d.name || ' ' || {$distributionTableNameAlias}.entity_stream_percent || '%'")]);
                return $query->leftJoin(['rsu' => RokeenteSessionUsers::TABLE_NAME], 'u.user_id = rsu.user_id and u.site_id = rsu.site_id')
                    ->leftJoin(['rs' => RokeenteSessions::TABLE_NAME], 'rs.session_id = rsu.session_id')
                    ->leftJoin(['ua' => UserAgents::TABLE_NAME], 'rs.server_useragent_id = ua.id')
                    ->leftJoin(['ua_d' => UseragentDevices::TABLE_NAME], 'ua_d.id = ua.device_id')
                    ->leftJoin([$distributionTableNameAlias => LyraStreamAtypicalDistributionStats::TABLE_NAME], "r.webmaster_id = lsads.webmaster_id and u.site_id = lsads.site_id and ua.device_id = {$distributionTableNameAlias}.entity_id and {$distributionTableNameAlias}.entity_type = :dt", ['dt' => $this->distributionType]);
            },
            LyraStreamAtypicalDistributionStat::ENTITY_TYPE_REG_CITY_DISTRIBUTION => function (Query $query) use ($distributionTableNameAlias) {
                $query->addSelect([static::RESULT_COL_INFO => new Expression("c.name || ' ' || {$distributionTableNameAlias}.entity_stream_percent || '%'")]);
                return $query
                    ->leftJoin(['c' => Cities::TABLE_NAME], "c.id = u.city_id")
                    ->leftJoin([$distributionTableNameAlias => LyraStreamAtypicalDistributionStats::TABLE_NAME], "r.webmaster_id = lsads.webmaster_id and u.site_id = lsads.site_id and u.city_id = {$distributionTableNameAlias}.entity_id and {$distributionTableNameAlias}.entity_type = :dt", ['dt' => $this->distributionType]);
            },
            default => throw new \RuntimeException('Unknown distribution type id: ' . $this->distributionType),
        };

        $query = $f($query);

        return $query->where([$this->streamSizeOperator, 'lsads.entity_stream_size', $this->streamSize]);
    }

    public function getParamsSignatureParts(): array
    {
        return [
            'distributionType',
            'rulesMap',
            'period',
            'streamSize',
            'streamSizeOperator',
        ];
    }

    public function getMessage(): string
    {
        $parts = [];
        foreach ($this->rulesMapToArray() as $usersCount => $positiveCasePercent) {
            $parts[] = "Atypical devices in stream with {$usersCount} users >= $positiveCasePercent";
        }
        return implode(', ', $parts);
    }

    private function rulesMapToArray(): array
    {
        if (!is_null($this->rulesMapArray)) {
            return $this->rulesMapArray;
        }

        $this->rulesMapArray = [];
        foreach (explode("\n", $this->rulesMap) as $line) {
            $matches = [];
            if (!preg_match('/^([0-9]+)\s([0-9]+)%$/i', $line, $matches)) {
                throw new \InvalidArgumentException('Not valid value!');
            }
            [, $usersCount, $positiveCasePercent] = $matches;
            $this->rulesMapArray[$usersCount] = (float)$positiveCasePercent;
        }

        return $this->rulesMapArray;
    }


    public function getLabel(): string
    {
        return "<span class=\"badge bg-secondary\"'>{$this->getMessage()}</span>";
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectCell(3, 'distributionType', 'Entity type', [
                    'list' => Arr::assocToIdName(LyraStreamAtypicalDistributionStat::ALLOWED_DISTRIBUTION_TYPES),
                    'multiple' => false,
                ]),
                $this->textAreaCell(3, 'rulesMap', 'Positive check rules', [
                    'hint' => 'Rule map for each line. Example: 5 60% means 5 users and greater have min 60% level of atypical devices'
                ]),
                $this->textInputCell(3, 'streamSize', 'Stream size', [
                    'operators' => Arr::assocToIdName([
                        self::OPERATOR_GREATER_OR_EQ => self::OPERATOR_GREATER_OR_EQ,
                        self::OPERATOR_LOWER_OR_EQ => self::OPERATOR_LOWER_OR_EQ,
                        self::OPERATOR_GREATER => self::OPERATOR_GREATER,
                        self::OPERATOR_LOWER => self::OPERATOR_LOWER,
                    ]),
                    'operatorPostfix' => 'Operator',
                ]),
                $this->textInputCell(3, 'period', 'Period (days)'),
            ]
        ];
    }

    public static function validateRulesMap(string $value, self $form, array $context): ?string
    {
        try {
            $form->rulesMap = $value;
            $form->rulesMapToArray();
            return null;
        } catch (\InvalidArgumentException $e) {
            return $e->getMessage();
        }
    }
}
