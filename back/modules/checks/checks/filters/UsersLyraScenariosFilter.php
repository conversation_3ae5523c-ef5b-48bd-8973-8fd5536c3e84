<?php

declare(strict_types=1);

namespace app\back\modules\checks\checks\filters;

use app\back\components\AllowedLists;
use app\back\components\helpers\Arr;
use app\back\components\RepositoryFactory;
use app\back\components\validators\AllowedSitesValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\repositories\Sites;

class UsersLyraScenariosFilter extends BaseFilter
{
    use SiteIdsLabelTrait;

    public const string COL_SITE_ID = 'site_id';
    public const string COL_USER_ID = 'user_id';
    public const string COL_UPDATED_AT = 'updated_at';

    #[AllowedSitesValidator]
    public ?array $siteId = [];
    #[IntInArrayValidator(self::INCL_EXCL_LIST)]
    public int $siteExclude = self::INCL;

    private Sites $sitesRepo;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        RepositoryFactory $repositoryFactory,
    ) {
        $this->sitesRepo = $repositoryFactory->create(Sites::class);
    }

    public function getLabel(): string
    {
        return implode(' ', $this->siteIdsLabel());
    }

    public function check(array $row): bool
    {
        return !(!empty($this->siteId) && ($this->siteExclude === (int)in_array($row[self::COL_SITE_ID], $this->siteId, true)));
    }

    protected function blockRows(): array
    {
        return [
            [
                $this->selectCell(12, 'siteId', 'Site', array_merge($this->allowedLists->getSitesWithGroups(), [
                    'operators' => Arr::assocToIdName(self::INCL_EXCL_LIST),
                    'operatorName' => 'siteExclude',
                ]))
            ]
        ];
    }
}
