<?php

declare(strict_types=1);

namespace app\back\modules\finance\faceMatching;

use app\back\components\AllowedLists;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\Json;
use app\back\components\helpers\Str;
use app\back\components\SecondaryConnection;
use app\back\components\services\FileStorage;
use app\back\components\SiteUserBuilder;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\DateValidator;
use app\back\components\validators\FloatValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\SiteUserIdValidator;
use app\back\components\validators\StringArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\User;
use app\back\entities\UserDocument;
use app\back\entities\UserDocumentFaceSimilarity;
use app\back\repositories\Employees;
use app\back\repositories\UserDocumentFaces;
use app\back\repositories\UserDocumentFaceSimilarities;
use app\back\repositories\views\UserDocumentsActive;
use app\back\repositories\Users;
use Yiisoft\Db\Expression\Expression;
use Yiisoft\Db\Query\Query;

class FaceMatchingSearchForm
{
    use FormGrid {
        response as formResponse;
    }

    private const string MODE_SEARCH = 'search';
    private const string COMMON_ACCOUNT = 'account';
    private const string COMMON_PHOTO = 'photo';
    private const string COMMON_NOTHING = 'nothing';
    private const array COMMON_DATA = [
        self::COMMON_ACCOUNT,
        self::COMMON_PHOTO,
        self::COMMON_NOTHING,
    ];

    private const string APPROVE_YES = 'yes';
    private const string APPROVE_NO = 'no';
    private const string APPROVE_UNDEFINED = 'undefined';
    private const array APPROVE_STATUSES = [
        self::APPROVE_YES => 'Yes',
        self::APPROVE_NO => 'No',
        self::APPROVE_UNDEFINED => 'Undefined',
    ];

    private const string FACE_A_B = 'face_a_b';
    private const string SITE_USER = 'site_user';
    private const string DISTANCE = 'distance';

    private const array SORT = [
        self::FACE_A_B => 'Face AB',
        self::SITE_USER => 'Site-User',
        self::DISTANCE => 'Distance',
    ];
    private const array SORT_DIR = [
        SORT_ASC => 'Asc',
        SORT_DESC => 'Desc',
    ];

    #[IdValidator]
    public ?int $faceId = null;
    #[StringInArrayValidator(self::COMMON_DATA, true)]
    public ?string $commonData = null;
    #[SiteUserIdValidator]
    public ?string $siteUser = null;
    #[StringArrayValidator(self::APPROVE_STATUSES)]
    public ?array $approved = [];
    #[FloatValidator]
    public ?float $fromDistance = null;
    #[FloatValidator]
    public ?float $toDistance = null;
    #[DateValidator]
    public ?string $from = null;
    #[DateValidator]
    public ?string $to = null;
    #[IntValidator(1, 500)]
    public int $limit = 100;
    #[IntArrayValidator(0, 2)]
    public ?array $hqFacesCount = null;
    #[BooleanValidator]
    public bool $filterNotBankCard = false;
    #[BooleanValidator]
    public bool $filterLocaleRu = false;
    #[BooleanValidator]
    public bool $calcTotal = false;
    #[StringInArrayValidator(self::SORT)]
    public string $sort_column = self::FACE_A_B;
    #[IntInArrayValidator(self::SORT_DIR)]
    public int $sort_dir = SORT_ASC;
    #[IdValidator]
    public int $page = 1;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly SecondaryConnection $db,
        private readonly FileStorage $storage,
        private readonly SiteUserBuilder $siteUserBuilder,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->textInputCell(2, 'faceId', 'Face ID'),
                $this->dateCell(2, 'from', 'From (created)'),
                $this->dateCell(2, 'to', 'To (created)'),
                $this->textInputCell(1, 'siteUser', 'Site-User'),
                $this->textInputCell(1, 'fromDistance', 'Distance >='),
                $this->textInputCell(1, 'toDistance', 'Distance <'),
                $this->listCell(2, 'commonData', 'Same', [
                    'list' => Arr::assocToIdName([
                        self::COMMON_ACCOUNT => 'Account',
                        self::COMMON_PHOTO => 'Photo',
                        self::COMMON_NOTHING => 'Nothing',
                    ]),
                    'multiple' => false,
                ]),
            ],
            [
                $this->listCell(2, 'approved', 'Approved', [
                    'list' => Arr::assocToIdName(self::APPROVE_STATUSES),
                ]),
                $this->listCell(1, 'hqFacesCount', 'HQ Faces', [
                    'list' => Arr::assocToIdName(['0', '1', '2']),
                ]),
                $this->checkboxCell(1, 'filterNotBankCard', 'Not Bank Card'),
                $this->checkboxCell(1, 'filterLocaleRu', 'Locale RU'),
                $this->checkboxCell(1, 'calcTotal', 'Calc total'),
                $this->textInputCell(1, 'limit', 'Limit'),
                $this->selectCell(1, 'sort_column', 'Sort column', [
                    'list' => Arr::assocToIdName(self::SORT),
                    'multiple' => false,
                ]),
                $this->listCell(1, 'sort_dir', 'Sort dir', [
                    'list' => Arr::assocToIdName(self::SORT_DIR),
                    'multiple' => false,
                ]),
                $this->submitCell(1, 'Search'),
            ],
        ];
    }

    public function composeResponse(): array
    {
        $res = [];
        $query = $this->getMainQuery();
        $rows = $query->all();

        if (count($rows) === $this->limit + 1) {
            array_pop($rows);
            $loadMore = true;
        } else {
            $loadMore = false;
        }

        foreach ($rows as $row) {
            [$focusFace, $matchFace] = array_map(static fn($faceJson) => Json::decode($faceJson), [$row['face'], $row['face2']]);

            if ($this->siteUser) {
                ['siteId' => $siteId, 'userId' => $userId] = $this->siteUserBuilder->valueToSiteUser($this->siteUser);
                if ($matchFace['site_id'] === (int)$siteId && $matchFace['user_id'] === (int)$userId) {
                    [$matchFace, $focusFace] = [$focusFace, $matchFace];
                }
            }
            if (!isset($res[$focusFace['id']])) {
                $res[$focusFace['id']] = $this->getFace($focusFace);
            }

            $res[$focusFace['id']]['matches'][] = array_merge($this->getFace($matchFace), [
                'distance' => $row['distance'],
                'approved' => $row['approved'],
                'employee' => Str::emailToLdap($row['employee_email']),
            ]);
        }

        if ($loadMore) {
            $next = ['page' => ++$this->page];
        } else {
            $next = null;
        }

        // not set form values directly to remove from url
        $this->page = 1;

        return [
            'data' => Arr::sortByKeys($res, ['siteUser']),
            'form' => $this->formResponse(),
            'totalMatches' => $this->calcTotal ? $query->count() : null,
            'next' => $next,
        ];
    }

    private function getMainQuery(): Query
    {
        $orderBy = match ($this->sort_column) {
            self::FACE_A_B => ['udfs.face_a_id' => SORT_ASC, 'udfs.face_b_id' => SORT_ASC],
            self::SITE_USER => ['ud.site_id' => $this->sort_dir, 'ud.user_id' => $this->sort_dir, 'ud2.site_id' => $this->sort_dir, 'ud2.user_id' => $this->sort_dir],
            self::DISTANCE => [$this->sort_column => $this->sort_dir],
        };

        $query = (new Query($this->db))
            ->select([
                'face' => $this->jsonBuildObject([
                    'site_id' => 'ud.site_id',
                    'user_id' => 'ud.user_id',
                    'filename' => 'ud.filename',
                    'height' => 'ud.height',
                    'width' => 'ud.width',
                    'id' => 'udf.id',
                    'box' => 'udf.box',
                    'created_at' => 'ud.created_at::date',
                    'similar_id' => 'udf.similar_id',
                ]),
                'face2' => $this->jsonBuildObject([
                    'site_id' => 'ud2.site_id',
                    'user_id' => 'ud2.user_id',
                    'filename' => 'ud2.filename',
                    'height' => 'ud2.height',
                    'width' => 'ud2.width',
                    'id' => 'udf2.id',
                    'box' => 'udf2.box',
                    'created_at' => 'ud2.created_at::date',
                    'similar_id' => 'udf2.similar_id',
                ]),
                'udfs.distance',
                'udfs.approved',
                'employee_email' => 'e.email',
            ])
            ->from(['ud' => UserDocumentsActive::TABLE_NAME])
            ->innerJoin(['udf' => UserDocumentFaces::TABLE_NAME], 'udf.user_document_id = ud.id')
            ->innerJoin(['udfs' => UserDocumentFaceSimilarities::TABLE_NAME], 'udfs.face_a_id = udf.id')
            ->innerJoin(['udf2' => UserDocumentFaces::TABLE_NAME], 'udf2.id = udfs.face_b_id')
            ->innerJoin(['ud2' => UserDocumentsActive::TABLE_NAME], 'ud2.id = udf2.user_document_id')
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = udfs.updated_by')
            ->andWhere([
                'AND',
                ['IS NOT', 'udf.is_valid', new Expression('FALSE')],
                ['IS NOT', 'udf2.is_valid', new Expression('FALSE')],
            ])
            ->andFilterWhere(['udfs.approved' => array_map(static fn($v) => match ($v) {
                self::APPROVE_YES => true,
                self::APPROVE_NO => false,
                self::APPROVE_UNDEFINED => null,
            }, $this->approved)])
            ->andFilterWhere([
                'AND',
                ['OR', ['udf.similar_id' => $this->faceId], ['udf2.similar_id' => $this->faceId]],
                ['OR', ['>=', 'ud.created_at', $this->from], ['>=', 'ud2.created_at', $this->from]],
                ['OR', ['<', 'ud.created_at', $this->to], ['<', 'ud2.created_at', $this->to]],
                ['>=', 'udfs.distance', (string)$this->fromDistance],
                ['<', 'udfs.distance', (string)$this->toDistance],
            ])
            ->orderBy($orderBy)
            ->limit($this->limit + 1)
            ->offset(($this->page - 1) * $this->limit);

        if ($this->siteUser) {
            ['siteId' => $siteId, 'userId' => $userId] = $this->siteUserBuilder->valueToSiteUser($this->siteUser);
            $query->andWhere([
                'OR',
                ['AND', ['ud.site_id' => $siteId], ['ud.user_id' => $userId]],
                ['AND', ['ud2.site_id' => $siteId], ['ud2.user_id' => $userId]],
            ]);
        }

        if ($this->commonData) {
            $query->andWhere(match ($this->commonData) {
                self::COMMON_ACCOUNT => 'ud2.site_id = ud.site_id AND ud2.user_id = ud.user_id',
                self::COMMON_PHOTO => 'udf.user_document_id = udf2.user_document_id',
                self::COMMON_NOTHING => 'ud2.site_id != ud.site_id OR ud2.user_id != ud.user_id',
            });
        }

        if ($this->hqFacesCount) {
            $query->andWhere(
                ['IN', '(select COUNT(*) from (values (udf.ratio), (udf2.ratio)) as face(ratio) where face.ratio > :low_ratio)', $this->hqFacesCount],
                ['low_ratio' => UserDocumentFaceSimilarity::FACE_QUALITY_RATIO_LOW]
            );
        }

        if ($this->filterNotBankCard) {
            $query->andWhere('ud.tags != array[:bank_card]::varchar[] AND ud2.tags != array[:bank_card]::varchar[]', [
                ':bank_card' => UserDocument::TAG_BANK_CARD
            ]);
        }

        if ($this->filterLocaleRu) {
            $query
                ->innerJoin(['u' => Users::TABLE_NAME], 'u.site_id = ud.site_id AND u.user_id = ud.user_id AND u.locale = :locale_ru')
                ->innerJoin(['u2' => Users::TABLE_NAME], 'u2.site_id = ud2.site_id AND u2.user_id = ud2.user_id AND u2.locale = :locale_ru')
                ->addParams([':locale_ru' => User::LOCALE_RU]);
        }

        return $query;
    }

    private function jsonBuildObject(array $fieldsMap): string
    {
        $fieldsList = implode(', ', array_map(
            static fn($expr, $filedName) => "'$filedName', $expr",
            array_values($fieldsMap),
            array_keys($fieldsMap),
        ));
        return "jsonb_build_object($fieldsList)";
    }

    private function getImageUrl(int $siteId, int $userId, string $filename): string
    {
        static $cache;

        if ($cache === null) {
            $cache = [];
        }

        $key = implode('/', [$siteId, $userId, $filename]);

        if (!array_key_exists($key, $cache)) {
            $cache[$key] = $this->storage->getPublicUrlByKey($key);
        }

        return $cache[$key];
    }

    private function getFace(array $face): array
    {
        return [
            'id' => $face['id'],
            'siteUser' => $this->siteUserBuilder->siteUserToValue($face['site_id'], $face['user_id']),
            'siteId' => $face['site_id'],
            'userId' => $face['user_id'],
            'similarId' => $face['similar_id'],
            'createdAt' => $face['created_at'],
            'box' => $face['box'],
            'doc' => [
                'src' => $this->getImageUrl($face['site_id'], $face['user_id'], $face['filename']),
                'width' => $face['width'],
                'height' => $face['height']
            ],
        ];
    }
}
