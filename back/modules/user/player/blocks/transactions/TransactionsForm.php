<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\transactions;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\helpers\DateHelper;
use app\back\components\MaskGenerator;
use app\back\components\Permission;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\components\validators\DateValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\jsonTypes\EmployeeSettings;
use app\back\entities\UserTransaction;
use app\back\entities\Withdrawal;
use app\back\modules\reports\columns\Operators;
use app\back\modules\reports\reports\Payments\PaymentsConfig;
use app\back\modules\user\player\blocks\BasePlayerForm;
use app\back\repositories\Employees;
use app\back\repositories\PaySystems;
use app\back\repositories\S2pOrders;
use app\back\repositories\S2pPaySystems;
use app\back\repositories\S2pStatuses;
use app\back\repositories\UserTransactions;
use app\back\repositories\Withdrawals;
use Yiisoft\Db\Query\Query;

class TransactionsForm extends BasePlayerForm
{
    use RichTable;

    private const string DATA_SOURCE_PRODUCT = 'product';
    private const string DATA_SOURCE_S2P = 's2p';

    public const array ALL_COLUMNS = [
        ['name' => 'Transaction ID', 'code' => 'transaction_id'],
        ['name' => 'S2P ID', 'code' => 's2p_order_id'],
        ['name' => 'Created', 'code' => 'created_at', 'sortable' => true],
        ['name' => 'Updated', 'code' => 'updated_at', 'slotName' => 'updated_at', 'sortable' => true],
        ['name' => 'Type', 'code' => 'op_id'],
        ['name' => 'Status', 'code' => 'status', 'slotName' => 'status'],
        ['name' => 'Sum', 'code' => 'sum'],
        ['name' => 'Sum total', 'code' => 'sum_locked'],
        ['name' => 'Pay sys', 'code' => 'pay_sys'],
        ['name' => 'End balance', 'code' => 'balance_after_changes'],
        ['name' => 'Requisite', 'code' => 'requisite', 'slotName' => 'requisite'],
        ['name' => 'Wallet (to)', 'code' => 'to_wallet_id', 'slotName' => 'to_wallet'],
        ['name' => 'Wallet (from)', 'code' => 'from_wallet_id', 'slotName' => 'from_wallet'],
        ['name' => 'Comment', 'code' => 'comment'],
        ['name' => 'Comment (adm)', 'code' => 'comment_admin', 'slotName' => 'comment_admin'],
    ];

    /**
     * Will be showed, only if selected in personal settings
     * @see EmployeeSettings::SETTING_PLAYER_TRANSACTIONS_EXTRA_COLUMNS
     */
    public const array EXTRA_COLUMN_CODES = [
        's2p_order_id',
        'created_at',
        'sum_locked',
        'balance_after_changes',
        'to_wallet_id',
        'from_wallet_id',
        'comment',
        'planned_at',
    ];

    #[DateValidator]
    public ?string $from = null;
    #[DateValidator]
    public ?string $to = null;
    #[StringValidator(1, 36)]
    public ?string $transId = null;
    #[IntArrayValidator(UserTransaction::OPERATIONS)]
    public array $opId = [];
    #[IntArrayValidator(UserTransaction::STATUSES)]
    public array $status = [];

    private array $totals;
    private bool $allowManualSuccess;

    public function __construct(
        AllowedLists $allowedLists,
        SecondaryConnection $db,
        BaseAuthAccess $auth,
        private readonly MaskGenerator $maskGenerator,
        private readonly PaySystems $paySystemsRepo,
        private readonly S2pPaySystems $s2pPaySystemsRepo,
        private readonly S2pStatuses $s2pStatusesRepo,
        private readonly Employees $employeesRepo,
    ) {
        parent::__construct($allowedLists, $db, $auth);
        $this->from = $this->getSettingFrom();
        $this->sort = 'updated_at-';
        $this->allowManualSuccess = $this->auth->can(Permission::PERM_ALLOW_DEP_MANUAL_SUCCESS);
    }

    protected function title(): string
    {
        $totals = $this->totals();
        $totals['avg'] = round((float)$totals['avg'], 2);
        $totals['sum'] = round((float)$totals['sum']);

        if ($totals['sum'] !== 0.0) {
            $totals['sum'] .= " {$totals['currency']}";
        }

        if ($totals['avg'] !== 0.0) {
            $totals['avg'] .= " {$totals['currency']}";
        }

        return "Transactions (Total count: {$totals['count']}, sum: {$totals['sum']}, avg: {$totals['avg']})";
    }

    protected function total(): int
    {
        $totals = $this->totals();

        return $totals['count'];
    }

    private function totals(): array
    {
        if (!isset($this->totals)) {
            $to = $this->to ? DateHelper::nextDay($this->to) : null;

            $this->totals = (new Query($this->db))
                ->select([
                    'count' => 'COUNT(*)',
                    'avg' => 'AVG(us.amount_orig)',
                    'sum' => 'SUM(us.amount_orig)',
                    'currency' => "STRING_AGG(DISTINCT currency, ', ')",
                ])
                ->from(['us' => UserTransactions::TABLE_NAME])
                ->where($this->siteUser())
                ->andFilterWhere(['us.status' => $this->status])
                ->andFilterWhere(['us.op_id' => $this->opId])
                ->andFilterWhere(['>=', 'us.updated_at', $this->from])
                ->andFilterWhere(['<', 'us.updated_at', $to])
                ->one();
        }

        return $this->totals;
    }

    protected function columns(array $context): array
    {
        $extraColumnsFromSettings = $this->auth->employee()->getSettingsObject()->getPlayerTransactionsExtraColumns();
        $columnsToDelete = array_diff(self::EXTRA_COLUMN_CODES, $extraColumnsFromSettings);

        return array_values(array_filter(static::ALL_COLUMNS, static fn ($c) => !in_array($c['code'] ?? null, $columnsToDelete, true)));
    }

    protected function blocks(): array
    {
        $to = $this->to ? DateHelper::nextDay($this->to) : null;
        $siteUser = $this->siteUser();

        $opIds = (new Query($this->db))
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->select([
                'id' => 'us.op_id',
                'count' => 'COUNT(*)',
            ])
            ->where($siteUser)
            ->andFilterWhere(['us.status' => $this->status])
            ->andFilterWhere(['>=', 'us.updated_at', $this->from])
            ->andFilterWhere(['<', 'us.updated_at', $to])
            ->groupBy(['us.op_id'])
            ->orderBy(['us.op_id' => SORT_ASC])
            ->all();

        foreach ($opIds as &$op) {
            $op['name'] = UserTransaction::getOperationById($op['id']);
        }
        unset($op);

        $statuses = (new Query($this->db))
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->select([
                'id' => 'us.status',
                'count' => 'COUNT(*)',
            ])
            ->where($siteUser)
            ->andFilterWhere(['us.op_id' => $this->opId])
            ->andFilterWhere(['>=', 'us.updated_at', $this->from])
            ->andFilterWhere(['<', 'us.updated_at', $to])
            ->groupBy(['us.status'])
            ->orderBy(['us.status' => SORT_ASC])
            ->all();

        foreach ($statuses as &$status) {
            $status['name'] = UserTransaction::getStatusById($status['id']);
        }

        return [
            [
                $this->dateCell(2, 'from', 'From'),
                $this->dateCell(2, 'to', 'To', [
                    'buttonsMode' => 'end',
                    'placeholder' => 'now',
                ]),
                $this->textInputCell(2, 'transId', 'Trans / Order id'),
                $this->selectCell(2, 'opId', 'Type', [
                    'list' => $opIds,
                ]),
                $this->listCell(4, 'status', 'Status', [
                    'list' => $statuses,
                ]),
            ],
        ];
    }

    public function data(): array
    {
        $select = [
            'source' => '(:source_product)',
            'site_id' => 'us.site_id',
            'user_id' => 'us.user_id',
            'transaction_id' => 'us.transaction_id',
            's2p_order_id' => 'o.id',
            'created_at' => 'us.created_at',
            'updated_at' => 'us.updated_at',
            'op_id' => 'us.op_id',
            'status' => 'us.status',
            'sum' => "(us.amount_orig || ' ' || us.currency)",
            'sum_locked' => "(us.amount_locked || ' ' || us.currency)",
            'pay_sys' => 'us.pay_sys_id',
            'balance_after_changes' => "(us.balance_after_changes || ' ' || us.currency)",
            'requisite' => 'COALESCE(o.requisite, us.wallet)',
            'card_holder' => 'o.card_holder',
            'comment' => 'us.comment',
            'comment_admin' => 'us.comment_admin',
            'status_detail' => 'o.status_id',
            'to_wallet_id' => 'us.to_wallet_id',
            'from_wallet_id' => 'us.from_wallet_id',
            'planned_at' => 'w.planned_at',
            'withdrawal_decision_at' => 'w.created_at',
            'operator_id' => 'w.operator_id',
        ];

        $to = $this->to ? DateHelper::nextDay($this->to) : null;

        $queryUsersTransactions = (new Query($this->db))
            ->select($select)
            ->from(['us' => UserTransactions::TABLE_NAME])
            ->leftJoin(['o' => S2pOrders::TABLE_NAME], 'o.site_id = us.site_id AND o.user_id = us.user_id AND o.invoice_id = us.transaction_id')
            ->leftJoin(['w' => Withdrawals::TABLE_NAME], ['AND', 'us.site_id = w.site_id', 'us.transaction_id = w.transaction_id', ['w.status' => [Withdrawal::STATUS_NEW, Withdrawal::STATUS_PLANNED, Withdrawal::STATUS_SYNCED]]])
            ->andFilterWhere(['us.transaction_id' => $this->transId])
            ->addParams([':source_product' => self::DATA_SOURCE_PRODUCT]);

        $queryS2pOrders = (new Query($this->db))
            ->select($this->overwriteOrNullSelects($select, [
                'source' => '(:source_s2p)',
                'site_id' => 'o.site_id',
                'user_id' => 'o.user_id',
                's2p_order_id' => 'o.id',
                'pay_sys' => 'o.pay_sys_id',
                'created_at' => 'o.date_created',
                'updated_at' => 'o.date',
                'sum' => "(o.client_original_amount || ' ' || o.client_original_currency)",
                'requisite' => 'o.requisite',
                'card_holder' => 'o.card_holder',
                'status_detail' => 'o.status_id',
            ]))
            ->from(['o' => S2pOrders::TABLE_NAME])
            ->leftJoin(['us' => UserTransactions::TABLE_NAME], 'o.site_id = us.site_id AND o.user_id = us.user_id AND o.invoice_id = us.transaction_id')
            ->andFilterWhere(['o.id' => $this->transId])
            ->andWhere(['us.transaction_id' => null])
            ->addParams([':source_s2p' => self::DATA_SOURCE_S2P]);

        $query = (new Query($this->db))
            ->from(['d' => $queryUsersTransactions->union($queryS2pOrders, true)])
            ->where($this->siteUser())
            ->andFilterWhere(['>=', 'updated_at', $this->from])
            ->andFilterWhere(['<', 'updated_at', $to])
            ->andFilterWhere(['op_id' => $this->opId])
            ->andFilterWhere(['status' => $this->status])
            ->orderBy($this->getOrderMap())
            ->offset($this->getOffset())
            ->limit($this->getLimit());

        $data = $query->all();

        foreach ($data as &$row) {
            if (
                UserTransaction::isManualSuccessEligible($this->siteId, $row['status'], $row['op_id'])
                && empty($row['operator_id']) // withdrawal isn't created yet
            ) {
                $row['allow_success_manual'] = $this->allowManualSuccess;
            }

            if (!empty($row['requisite']) && !$this->auth->canViewRequisites()) {
                $row['requisite'] = $this->maskGenerator->mask($row['requisite']);
            }

            if (!empty($row['card_holder']) && !$this->auth->canViewPersonalData()) {
                $row['card_holder'] = $this->maskGenerator->maskCardHolder($row['card_holder']);
            }

            if ($row['status_detail'] !== 0) {
                $row['status_detail'] = $this->s2pStatusesRepo->getNameById($row['status_detail']);
            }

            if (array_key_exists('op_id', $row)) {
                $row['op_id'] = UserTransaction::getOperationById($row['op_id']);
            }

            if (array_key_exists('status', $row)) {
                $row['status'] = UserTransaction::getStatusById($row['status']);
            }

            if (array_key_exists('pay_sys', $row)) {
                $row['pay_sys'] = (match ($row['source']) {
                    self::DATA_SOURCE_S2P => $this->s2pPaySystemsRepo,
                    default => $this->paySystemsRepo,
                })->getNameById($row['pay_sys']);
            }

            if (!empty($row['planned_at'])) {
                $row['planned_at_in_future'] = new \DateTimeImmutable($row['planned_at']) > new \DateTimeImmutable();
            }

            if (!empty($row['comment_admin']) && $row['comment_admin'] === Withdrawal::MANUAL_SUCCESS_ADMIN_COMMENT) {
                $row['manual_approved_by'] = $this->employeesRepo->getNameById($row['operator_id']);
            }

            unset($row['source'], $row['site_id'], $row['user_id'], $row['operator_id']);
        }

        return $data;
    }

    private function overwriteOrNullSelects(array $selectsOrig, array $selectOverwrite): array
    {
        foreach ($selectsOrig as $key => $val) {
            $selectsOrig[$key] = $selectOverwrite[$key] ?? '(NULL)';
        }
        return $selectsOrig;
    }

    public function additionalResponse(): array
    {
        return [
            'paymentsReportHref' => PaymentsConfig::url([
                'filters' => [
                    ['site_id', [$this->siteId]],
                    ['user_id', $this->userId, Operators::IN],
                    ['date', $this->from, '>='],
                    ['date_type', 'updated_at'],
                ],
                'columns' => ['currency', 'site_id', 'user_id', 'created_at', 'updated_at', 'op_id', 'ext_type', 'dir', 'status', 'sum_orig']
            ]),
            'docRequestAvailable' => $this->auth->can(Permission::PAGE_DOCUMENTS) && $this->auth->can(Permission::PAGE_DOCUMENTS_KYC),
        ];
    }
}
