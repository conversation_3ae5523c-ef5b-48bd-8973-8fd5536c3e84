<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\customer;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\Form;
use app\back\components\validators\IdValidator;
use app\back\entities\Customer;
use app\back\entities\CustomerUser;
use app\back\modules\user\player\forms\BasePlayerForm;
use app\back\repositories\CustomerLogs;
use app\back\repositories\Customers;
use app\back\repositories\CustomerUsers;

class CustomerLinkForm extends BasePlayerForm
{
    use Form;

    #[IdValidator]
    public int $customerId;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly BaseAuthAccess $auth,
        private readonly Customers $customersRepo,
        private readonly CustomerUsers $customerUsersRepo,
        private readonly CustomerLogs $customerLogsRepo,
    ) {
    }

    public function link(): void
    {
        /** @var Customer $customer */
        $customer = $this->customersRepo->findOneOr404([
            'id' => $this->customerId,
        ]);

        $customerUser = new CustomerUser([
            'user_id' => $this->userId,
            'site_id' => $this->siteId,
            'created_by' => $this->auth->employeeId(),
            'customer_id' => $customer->id,
        ]);
        $this->customerUsersRepo->insert($customerUser);

        $this->customerLogsRepo->logLink($customerUser);
    }
}
