<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\chats;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\DateHelper;
use app\back\components\SecondaryConnection;
use app\back\components\validators\DateValidator;
use app\back\entities\ChatMessage;
use app\back\modules\user\player\blocks\BasePlayerForm;
use app\back\repositories\ChatMessages;
use app\back\repositories\Chats;
use Yiisoft\Db\Query\Query;

class ChatsForm extends BasePlayerForm
{
    use FormGrid {
        validateAndResponse as formResponse;
    }

    #[DateValidator]
    public ?string $from = null;
    #[DateValidator]
    public ?string $to = null;

    public function __construct(
        AllowedLists $allowedLists,
        SecondaryConnection $db,
        BaseAuthAccess $auth,
    ) {
        parent::__construct($allowedLists, $db, $auth);
        $this->from = $this->getSettingFrom();
    }

    protected function blocks(): array
    {
        return [
            [
                $this->dateCell(2, 'from', 'From'),
                $this->dateCell(2, 'to', 'To', [
                    'buttonsMode' => 'end',
                    'placeholder' => 'now',
                ]),
            ],
        ];
    }

    public function getChats(): array
    {
        $to = $this->to ? DateHelper::nextDay($this->to) : null;

        $topicQuery = (new Query($this->db))
            ->select([
                'topic' => "STRING_AGG(cm.text, ', ')",
            ])
            ->where([
                'AND',
                'c.chat_id = cm.chat_id',
                ['cm.type' => ChatMessage::TYPE_TOPIC],
            ])
            ->from(['cm' => ChatMessages::TABLE_NAME]);

        $rows = (new Query($this->db))
            ->select([
                'id' => 'c.chat_id',
                'created_at' => 'c.created_at',
                'started_at' => 'c.started_at',
                'ended_at' => 'c.ended_at',
                'clientMessageCount' => 'c.client_message_count',
                'operatorMessageCount' => 'c.operator_message_count',
                'date' => 'COALESCE(c.started_at, c.created_at)::DATE',
                'topic' => $topicQuery,
            ])
            ->from(['c' => Chats::TABLE_NAME])
            ->where([
                'c.site_id' => $this->siteId,
                'c.user_id' => $this->userId,
            ])
            ->andFilterWhere(['>=', 'c.created_at', $this->from])
            ->andFilterWhere(['<', 'c.created_at', $to])
            ->groupBy('c.chat_id')
            ->orderBy([
                'date' => SORT_DESC,
                'c.created_at' => SORT_ASC,
            ])
            ->all();

        foreach ($rows as &$row) {
            if ($row['started_at'] !== null) {
                $row['length'] = ($row['started_at'] ? round((strtotime($row['ended_at']) - strtotime($row['started_at'])) / 60) : 0) . ' mins';
                $row['time'] = date('H:i', strtotime($row['started_at']));
            }
        }

        return $rows;
    }

    public function validateAndResponse(array $requestData): array
    {
        $formResponse = $this->formResponse($requestData);

        return [
            'form' => $formResponse,
            'chats' => $this->getChats(),
        ];
    }
}
