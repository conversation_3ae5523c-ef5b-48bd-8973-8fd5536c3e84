<?php

declare(strict_types=1);

namespace app\back\modules\user\player\blocks\bonusesActivation;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\exceptions\UserException;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\helpers\Json;
use app\back\components\helpers\Str;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\entities\BonusUserActivity;
use app\back\entities\Site;
use app\back\modules\task\components\FetchTaskFactory;
use app\back\modules\user\player\blocks\BasePlayerForm;
use app\back\repositories\BonusActivatedUsers;
use app\back\repositories\Bonuses;
use app\back\repositories\BonusUserActivities;
use Yiisoft\Db\Query\Query;

class BonusesActivationForm extends BasePlayerForm
{
    use RichTable;

    private array $prodBonusesIds = [];

    public function __construct(
        AllowedLists $allowedLists,
        SecondaryConnection $db,
        BaseAuthAccess $auth,
        private readonly FetchTaskFactory $requestFactory,
    ) {
        parent::__construct($allowedLists, $db, $auth);
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Bonus ID', 'code' => 'bonus_id', 'slotName' => 'bonus_id'],
            ['name' => 'Status', 'slotName' => 'status'],
            ['name' => 'Bonus name', 'code' => 'bonus_name'],
            ['name' => 'Started at', 'code' => 'started_at'],
            ['name' => 'Ended at', 'code' => 'ended_at'],
        ];
    }

    protected function blocks(): array
    {
        return [];
    }

    public function data(): array
    {
        $usersAvailableBonuses = $this->usersAvailableBonuses();

        foreach ($usersAvailableBonuses as &$bonus) {
            $this->prodBonusesIds[] = $bonus['bonus_id'];

            $bonus['started_at'] = date("Y-m-d H:i:s", strtotime($bonus['started_at']));
            $bonus['ended_at'] = date("Y-m-d H:i:s", strtotime($bonus['ended_at']));
            if (!empty($bonus['min_deposit'])) {
                $bonus['min_deposit'] = array_map(Str::roundUntilNonZero(...), $bonus['min_deposit']);
                $bonus['info_raw']['min_deposit'] = $bonus['min_deposit'];
            }


            $nonEmptyFields = [
                'type' => 'prize_type',
                'promocodes' => 'promocodes',
                'prizes' => 'prizes'
            ];

            foreach ($nonEmptyFields as $key => $field) {
                if (!empty($bonus[$key])) {
                    $bonus['info_raw'][$field] = $bonus[$key];
                }
            }
        }
        unset($bonus);

        $data = $this->buildQuery()
            ->select([
                'bonus_id' => 'bas.bonus_id',
                'started_at' => 'b.started_at',
                'ended_at' => 'b.ended_at',
                'bonus_name' => 'b.name',
                'activity' => 'asq.activity',
            ])
            ->orderBy(['b.started_at' => SORT_DESC])
            ->limit($this->getLimit())
            ->offset($this->getOffset())
            ->all();

        foreach ($data as &$datum) {
            $datum['status'] = strtotime($datum['ended_at']) < time();

            if (!empty($datum['activity'])) {
                $activity = Json::decode($datum['activity']);
                array_walk($activity, static function (&$el) use ($datum) {
                    $period = DateHelper::splitIntervalToTimeUnits($datum['started_at'], $el[0]);
                    $el[0] = "after {$period['hours']} h {$period['minutes']} min";
                    $el[1] = BonusUserActivity::getStatus((int) $el[1]);
                });
                $datum['activity'] = $activity;
            }
        }

        return array_merge($usersAvailableBonuses, $data);
    }

    private function buildQuery(): Query
    {
        $activitySubQuery = (new Query($this->db))
            ->select([
                'bua.site_id',
                'bua.user_id',
                'bua.bonus_id',
                'activity' => 'ARRAY_TO_JSON(ARRAY_AGG(ARRAY[created_at::text, status::text] ORDER BY created_at DESC))',
            ])
            ->from(['bua' => BonusUserActivities::TABLE_NAME])
            ->where([
                'AND',
                ['bua.site_id' => $this->siteId],
                ['bua.user_id' => $this->userId],
            ])
            ->groupBy(['bua.site_id', 'bua.user_id', 'bua.bonus_id']);

        $query = (new Query($this->db))
            ->from(['bas' => BonusActivatedUsers::TABLE_NAME])
            ->leftJoin(['b' => Bonuses::TABLE_NAME], 'b.site_id = bas.site_id AND b.bonus_id = bas.bonus_id')
            ->leftJoin(['asq' => $activitySubQuery], 'asq.site_id = bas.site_id AND asq.user_id = bas.user_id AND bas.bonus_id = asq.bonus_id');

        return $this->applyFilters($query);
    }

    private function applyFilters(Query $query): Query
    {
        return $query
            ->andWhere($this->siteUser('bas'))
            ->andFilterWhere([
                'AND',
                ['>=', 'b.started_at', DateHelper::monthAgo()],
                ['<=', 'b.started_at', DateHelper::today()],
                ['NOT IN', 'bas.bonus_id', $this->prodBonusesIds]
            ]);
    }

    public function total(): int
    {
        return $this->buildQuery()
            ->select('COUNT(*)')
            ->scalar();
    }

    private function usersAvailableBonuses(): array
    {
        if (!in_array($this->siteId, Site::PLATFORM_SITES_SMEN, true)) {
            throw new UserException('Bonuses activation block available only for SMEN users');
        }

        $fetchTask = $this->requestFactory->createFetchTask('users-bonuses-activation', $this->siteId, [
            'params' => [
                'user_id' => $this->userId,
            ],
        ]);

        return Arr::fromIterable($fetchTask->finalData());
    }
}
