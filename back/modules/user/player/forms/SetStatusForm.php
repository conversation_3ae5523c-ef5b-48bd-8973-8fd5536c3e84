<?php

declare(strict_types=1);

namespace app\back\modules\user\player\forms;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\validators\BooleanValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\components\validators\StringInArrayValidator;
use app\back\entities\User;
use app\back\entities\UserHistory;
use app\back\repositories\Users;

class SetStatusForm extends BasePlayerForm
{
    use FormGrid;

    #[StringInArrayValidator(['status', 'activeStatus', 'isManual'], true)]
    public string $column;
    #[IntInArrayValidator([User::class, 'actualStatuses'])]
    public ?int $status = null;
    #[IntInArrayValidator(User::ACTIVE_STATUSES)]
    public ?int $activeStatus = null;
    #[BooleanValidator]
    public ?bool $isManual = null;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly Users $usersRepo,
        private readonly BaseAuthAccess $auth,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->listCell(12, 'status', 'Status', [
                    'list' => Arr::assocToIdName(User::actualStatuses()),
                    'multiple' => false,
                ]),
            ],
            [
                $this->listCell(8, 'activeStatus', 'Active', [
                    'list' => Arr::assocToIdName(User::ACTIVE_STATUSES),
                    'multiple' => false,
                ]),
                $this->listCell(4, 'isManual', 'Is manual', [
                    'list' => [
                        ['id' => true, 'name' => 'Yes'],
                        ['id' => false, 'name' => 'No'],
                    ],
                    'multiple' => false,
                ]),
            ],
        ];
    }

    public function updateStatus(): void
    {
        $siteUser = [
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
        ];

        match ($this->column) {
            'status' => $this->usersRepo->updateStatus($siteUser, UserHistory::SOURCE_PLAYER, $this->status, null, true, $this->auth->employeeId()),
            'activeStatus' => $this->usersRepo->updateStatus($siteUser, UserHistory::SOURCE_PLAYER, null, $this->activeStatus, null, $this->auth->employeeId()),
            'isManual' => $this->usersRepo->updateStatus($siteUser, UserHistory::SOURCE_PLAYER, null, null, $this->isManual, $this->auth->employeeId()),
        };
    }
}
