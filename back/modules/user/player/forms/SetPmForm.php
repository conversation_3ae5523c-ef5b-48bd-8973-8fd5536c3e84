<?php

declare(strict_types=1);

namespace app\back\modules\user\player\forms;

use app\back\components\AllowedLists;
use app\back\components\BaseAuthAccess;
use app\back\components\FormGrid;
use app\back\components\validators\IntValidator;
use app\back\entities\User;
use app\back\entities\UserHistory;
use app\back\repositories\YhOperators;
use app\back\repositories\UserHistories;
use app\back\repositories\Users;

class SetPmForm extends BasePlayerForm
{
    use FormGrid;

    #[IntValidator]
    public ?int $pmId = null;

    public function __construct(
        public readonly AllowedLists $allowedLists,
        private readonly YhOperators $operatorsRepo,
        private readonly Users $usersRepo,
        private readonly UserHistories $userHistoriesRepo,
        private readonly BaseAuthAccess $auth,
    ) {
    }

    protected function blocks(): array
    {
        return [
            [
                $this->selectCell(12, 'pmId', 'Personal manager', [
                    'list' => $this->operatorsRepo->getIdNameDict(),
                    'multiple' => false,
                ])
            ],
        ];
    }

    public function savePm(): void
    {
        /** @var User $user */
        $user = $this->usersRepo->findOneOr404([
            'site_id' => $this->siteId,
            'user_id' => $this->userId,
        ]);

        $oldPm = $user->personal_manager;
        $user->personal_manager = $this->pmId ?? 0;

        $this->usersRepo->update($user, ['personal_manager']);
        $this->userHistoriesRepo->saveChanges($user, [UserHistory::COL_PERSONAL_MANAGER => $oldPm], UserHistory::SOURCE_PLAYER, $this->auth->employeeId());
    }
}
