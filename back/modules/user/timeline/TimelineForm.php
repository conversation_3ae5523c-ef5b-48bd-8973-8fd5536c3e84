<?php

declare(strict_types=1);

namespace app\back\modules\user\timeline;

use app\back\components\AllowedLists;
use app\back\components\Container;
use app\back\components\FormGrid;
use app\back\components\helpers\Arr;
use app\back\components\helpers\DateHelper;
use app\back\components\SecondaryConnection;
use app\back\components\validators\AllowedSiteValidator;
use app\back\components\validators\FilterValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\IpMultiLineValidator;
use app\back\components\validators\MatchValidator;
use app\back\components\validators\StringValidator;
use app\back\components\validators\UserIdOrSiteUserIdValidator;
use app\back\entities\Site;
use app\back\modules\user\timeline\events\{
    BaseEvent,
    BettingEvent,
    BlockUserEvent,
    CheckoutEvent,
    ContactEvent,
    CrmEvent,
    GameEvent,
    KycEvent,
    LoginEvent,
    PaymentEvent,
    RegistrationEvent,
    UserBonusActivityEvent,
    UserBonusEvent,
    UserEvent,
    UserTwoFactorAuthEvent,
    WheelFortunePrizeEvent,
    YhCallEvent,
    YhChatEvent,
    YhContactEvent,
    YhMailEvent};
use app\back\repositories\UserLogins;
use Yiisoft\Db\Query\Query;

class TimelineForm
{
    use FormGrid;

    public const int EVENTS_BREAK_PERIOD_SECONDS = 30 * 60; // half an hour

    public const int EVENT_TYPE_REGISTRATION = 1;
    public const int EVENT_TYPE_LOGIN = 2;
    public const int EVENT_TYPE_PAY = 3;
    public const int EVENT_TYPE_GAME = 4;
    public const int EVENT_TYPE_CRM = 5;
    public const int EVENT_TYPE_YH_CONTACT = 6;
    public const int EVENT_TYPE_YH_CHAT = 7;
    //public const int EVENT_TYPE_FREESPINS = 8;
    public const int EVENT_TYPE_OTHER = 9;
    public const int EVENT_TYPE_CONTACT_ADDED = 10;
    public const int EVENT_TYPE_USER_BONUS = 11;
    public const int EVENT_TYPE_YH_MAIL = 12;
    public const int EVENT_TYPE_YH_SMS = 13;
    public const int EVENT_TYPE_YH_CALL = 14;
    public const int EVENT_TYPE_WHEEL_FORTUNE_PRIZE = 15;
    //public const int EVENT_TYPE_BONUS_BLACK_LIST = 16;
    public const int EVENT_TYPE_BETTING = 17;
    public const int EVENT_TYPE_USER_BONUS_ACTIVITY = 18;
    public const int EVENT_TYPE_KYC = 19;
    public const int EVENT_TYPE_TWO_FACTOR_AUTH = 20;
    public const int EVENT_TYPE_BLOCK_USER = 21;
    public const int EVENT_TYPE_CHECKOUT = 22;

    public const array EVENT_TYPES_CLASSES = [
        self::EVENT_TYPE_REGISTRATION => RegistrationEvent::class,
        self::EVENT_TYPE_LOGIN => LoginEvent::class,
        self::EVENT_TYPE_PAY => PaymentEvent::class,
        self::EVENT_TYPE_GAME => GameEvent::class,
        self::EVENT_TYPE_CRM => CrmEvent::class,
        self::EVENT_TYPE_YH_CONTACT => YhContactEvent::class,
        self::EVENT_TYPE_YH_CHAT => YhChatEvent::class,
        self::EVENT_TYPE_OTHER => UserEvent::class,
        self::EVENT_TYPE_CONTACT_ADDED => ContactEvent::class,
        self::EVENT_TYPE_USER_BONUS => UserBonusEvent::class,
        self::EVENT_TYPE_YH_MAIL => YhMailEvent::class,
        self::EVENT_TYPE_YH_CALL => YhCallEvent::class,
        self::EVENT_TYPE_WHEEL_FORTUNE_PRIZE => WheelFortunePrizeEvent::class,
        self::EVENT_TYPE_BETTING => BettingEvent::class,
        self::EVENT_TYPE_USER_BONUS_ACTIVITY => UserBonusActivityEvent::class,
        self::EVENT_TYPE_KYC => KycEvent::class,
        self::EVENT_TYPE_TWO_FACTOR_AUTH => UserTwoFactorAuthEvent::class,
        self::EVENT_TYPE_BLOCK_USER => BlockUserEvent::class,
        self::EVENT_TYPE_CHECKOUT => CheckoutEvent::class,
    ];

    public const array EVENT_TYPES_NOT_ACTIVE_BY_DEFAULT = [
        self::EVENT_TYPE_CHECKOUT,
    ];

    private const int MONTHS = 2;

    #[AllowedSiteValidator]
    public ?int $siteId = Site::CV;

    #[StringValidator]
    #[MatchValidator(DateHelper::RANGE_PATTERN)]
    public ?string $dateRange = null;

    #[IpMultiLineValidator]
    public ?string $ip = null;

    #[UserIdOrSiteUserIdValidator]
    public string $value;

    #[FilterValidator([self::class, 'deprecatedToArray'])]
    #[IntArrayValidator(self::EVENT_TYPES_CLASSES)]
    public array $events = [];

    public function __construct(
        private readonly array $can,
        private readonly SecondaryConnection $db,
        private readonly Container $container,
        public readonly AllowedLists $allowedLists,
    ) {
        $this->events = array_diff(array_keys(self::EVENT_TYPES_CLASSES), self::EVENT_TYPES_NOT_ACTIVE_BY_DEFAULT);
    }

    public static function deprecatedToArray($value)
    {
        if (!is_array($value)) {
            $value = [$value];
        }

        return $value;
    }

    /** @return EventDto[] */
    public function getEvents(): array
    {
        $siteIdUserId = UserIdOrSiteUserIdValidator::siteIdUserIdFromValue($this->value, $this);
        if ($this->dateRange !== null) {
            [$from, $to] = DateHelper::splitRange($this->dateRange);
            $to = DateHelper::nextDay($to);
        } else {
            //take two months before the last login
            $lastActivity = $this->getLastActivity($siteIdUserId, self::MONTHS);
            if ($lastActivity === null) {
                $lastActivity = '2000-01-01';
            }
            $from = $lastActivity;
            $to = DateHelper::nextDay(DateHelper::today());
            $this->dateRange = DateHelper::range($lastActivity, DateHelper::nextDay(DateHelper::today()));
        }

        $filteredEventTypes = static::EVENT_TYPES_CLASSES;

        if (!empty($this->events)) {
            $filteredEventTypes = Arr::leaveOnlyKeys($filteredEventTypes, $this->events);
        }

        $events = [];
        foreach ($filteredEventTypes as $eventClass) {
            /** @var BaseEvent $eventTypeClass */
            $eventTypeClass = $this->container->create($eventClass, [
                'siteIdUserId' => $siteIdUserId,
                'dateFrom' => $from,
                'dateTo' => $to,
                'ip' => $this->ip,
                'can' => $this->can
            ]);

            foreach ($eventTypeClass->getEvents() as $row) {
                $event = new EventDto();
                $event->date = $row['date'];
                $event->time = strtotime($row['time']);
                $event->tooltip['Time'] = date('H:i', $event->time);
                $eventTypeClass->rowToEvent($row, $event);

                $events[] = $event;
            }
        }

        usort($events, static function (EventDto $event1, EventDto $event2) {
            $res = $event2->date <=> $event1->date;
            if ($res === 0) {
                $res = $event1->time <=> $event2->time;
            }

            return $res;
        });

        return $events;
    }

    public function getData(): array
    {
        $events = $this->getEvents();

        if (empty($events)) {
            return [];
        }

        $groups = [];
        $prevTime = $blockTime = reset($events)->time;

        foreach ($events as $e) {
            $eventTime = $e->time;
            $eventDay = $e->date;

            if ($eventTime - $prevTime > static::EVENTS_BREAK_PERIOD_SECONDS) {
                // New events block
                $blockTime = $eventTime;
            }
            $prevTime = $eventTime;

            if (!isset($groups[$eventDay])) {
                $groups[$eventDay] = [];
            }

            if (!isset($groups[$eventDay][$blockTime])) {
                $groups[$eventDay][$blockTime] = [
                    'start' => date('H:i', $prevTime),
                    'end' => date('H:i', $blockTime),
                    'events' => [],
                ];
            }

            $groups[$eventDay][$blockTime]['events'][] = $e->toArray();
            $groups[$eventDay][$blockTime]['end'] = date('H:i', $eventTime); // Refresh block end time
        }

        $res = [];
        foreach ($groups as $date => $dayGroup) {
            $res[] = [
                'date' => $date,
                'block' => array_values($dayGroup),
            ];
        }

        return [
            'timeline' => $res,
            'form' => $this->params(),
        ];
    }

    protected function blocks(): array
    {
        $events = Arr::assocToIdName(array_map(static fn($v) => /** @var BaseEvent $v */ $v::getEventTypeName(), self::EVENT_TYPES_CLASSES));

        return [
            [
                $this->selectSiteCell(2, 'siteId', 'Site', [
                    'multiple' => false,
                ]),
                $this->textInputCell(3, 'value', 'User Id / Site-User Id', [
                    'submit-on-enter' => true,
                ]),
                $this->dateRangeCall(3, 'dateRange', 'Date range'),
                $this->textAreaCell(2, 'ip', 'IP'),
                $this->submitCell(2, 'Search', ['buttonIcon' => 'icn-search'])
            ],
            [
                $this->listCell(12, 'events', 'Events', [
                    'size' => 'sm',
                    'list' => $events,
                ]),
            ],
        ];
    }

    private function getLastActivity(array $siteIdUserId, int $months): ?string
    {
        $result = (new Query($this->db))
            ->select('MAX(date)::date')
            ->from(['f' => UserLogins::TABLE_NAME])
            ->where($siteIdUserId)
            ->scalar();

        if ($result === false || $result === null) {
            return null;
        }

        return date('Y-m-d', strtotime("- {$months} months", strtotime($result)));
    }
}
