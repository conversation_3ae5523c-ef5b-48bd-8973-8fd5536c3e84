<?php

declare(strict_types=1);

namespace app\back\modules\back\help;

use app\back\components\Form;
use app\back\components\validators\StringValidator;
use app\back\repositories\HelpPages;

class HelpElementForm
{
    use Form;

    #[StringValidator(1, 250)]
    public string $url;
    #[StringValidator(1, 100)]
    public string $name;
    #[StringValidator(1, 200)]
    public ?string $value = null;

    public function __construct(
        private readonly HelpPages $helpPages
    ) {
    }

    public function update(int $lastEditorID): void
    {
        $entity = $this->helpPages->findByUrl($this->url);
        $entity->elements[$this->name] = $this->value;
        $entity->last_editor_id = $lastEditorID;
        $this->helpPages->update($entity, ['elements', 'last_editor_id']);
    }
}
