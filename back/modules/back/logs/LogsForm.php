<?php

declare(strict_types=1);

namespace app\back\modules\back\logs;

use app\back\components\helpers\Arr;
use app\back\components\helpers\Str;
use app\back\components\helpers\Url;
use app\back\components\RichTable;
use app\back\components\SecondaryConnection;
use app\back\components\validators\DateTimeValidator;
use app\back\components\validators\IdMultilineValidator;
use app\back\components\validators\IntArrayValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\IpValidator;
use app\back\components\validators\StringMultilineValidator;
use app\back\entities\Log;
use app\back\modules\reports\components\BaseFormRequest;
use app\back\repositories\Employees;
use app\back\repositories\Logs;
use Yiisoft\Db\Query\Query;

class LogsForm
{
    use RichTable;

    #[IpValidator]
    public ?string $ip = null;
    #[StringMultilineValidator]
    public ?string $employee = null;
    #[IdMultilineValidator]
    public ?string $employeeId = null;
    #[IntArrayValidator(Log::TYPES)]
    public array $type = [];
    #[DateTimeValidator]
    public ?string $from = null;
    #[DateTimeValidator]
    public ?string $to = null;
    #[StringMultilineValidator(0, 1000)]
    public ?string $url = null;
    #[StringMultilineValidator(0, 10000)]
    public ?string $params = null;
    #[StringMultilineValidator(0, 10000)]
    public ?string $paramsNot = null;
    #[IntValidator]
    public ?int $paramsLengthLowerThan = null;
    #[StringMultilineValidator(0, 10000)]
    public ?string $paramsJsonPath = null;

    public function __construct(private readonly SecondaryConnection $db)
    {
        $this->pageSize = 200;
        $this->from = date('Y-m-d 00:00:00');
    }

    protected function columns(array $context): array
    {
        return [
            ['name' => 'Date', 'code' => 'date'],
            ['name' => 'Time', 'code' => 'time'],
            ['name' => 'Employee', 'code' => 'employee', 'sortable' => true],
            ['name' => 'Type', 'code' => 'type'],
            ['name' => 'Url', 'code' => 'url', 'sortable' => true],
            ['name' => 'Params', 'code' => 'params', 'sortable' => true, 'slotName' => 'params'],
            ['name' => 'IP', 'code' => 'ip', 'sortable' => true],
        ];
    }

    protected function blocks(): array
    {
        return [
            [
                $this->dateTimeCell(1, 'from', 'From'),
                $this->dateTimeCell(1, 'to', 'To', ['buttonsMode' => 'end']),
                $this->textAreaCell(1, 'employee', 'Employee email'),
                $this->textAreaCell(1, 'employeeId', 'Employee id'),
                $this->selectCell(2, 'type', 'Type', ['list' => Arr::assocToIdName(Log::TYPES)]),
                $this->textInputCell(1, 'ip', 'Ip'),
                $this->textInputCell(1, 'paramsLengthLowerThan', 'Params length <='),
            ],
            [
                $this->textAreaCell(2, 'url', 'Url (like)'),
                $this->textAreaCell(3, 'params', 'Params (contains)'),
                $this->textAreaCell(3, 'paramsNot', 'Params (not contains)'),
                $this->textAreaCell(2, 'paramsJsonPath', 'Params (jsonPath expression)', [
                    'hint' => 'exists($ ? (@.siteId == $id) ? (@.userId >= $uid)) {"id": "27", "uid": "54733623"}'
                ]),
                $this->submitCell(1, 'Search', ['buttonIcon' => 'icn-search']),
            ],
        ];
    }

    public function data(): array
    {
        $rows = $this->dataQuery()
            ->select([
                'url' => 'l.url',
                'ip' => 'l.ip',
                'employee' => 'e.email',
                'type' => 'l.type',
                'created_at' => 'l.created_at',
                'params' => 'l.params',
            ])
            ->limit($this->getLimit())
            ->offset($this->getOffset())
            ->all();

        return $this->decorateData($rows);
    }

    public function download(): iterable
    {
        $rows = $this->dataQuery()
            ->select([
                'Employee' => 'e.email',
                'IP' => 'l.ip',
                'Created at' => 'l.created_at',
                'Type' => 'l.type',
                'Url' => 'l.url',
                'Request params' => 'l.params',
            ])
            ->each();

        foreach ($rows as $row) {
            $row['Type'] = Log::getTypeById($row['Type']);

            yield $row;
        }
    }

    private function dataQuery(): Query
    {
        $query = (new Query($this->db))
            ->from(['l' => Logs::TABLE_NAME])
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = l.employee_id')
            ->orderBy($this->getOrderMap())
            ->addOrderBy(['l.created_at' => SORT_DESC]);

        return $this->applyFilters($query);
    }

    private function decorateData(array $rows): array
    {
        foreach ($rows as &$row) {
            $createdAt = strtotime($row['created_at']);
            $row['date'] = date('Y-m-d', $createdAt);
            $row['time'] = date('H:i:s', $createdAt);
            $row['type'] = Log::getTypeById($row['type']);

            if ($row['url'] !== null && preg_match('#^/front/reports/(?<reportId>[^/]+)/(?:csv|data)#', $row['url'], $matches)) {
                $params = json_decode($row['params'], true, 512, JSON_THROW_ON_ERROR);

                $filters = $params['filters'] ?? [];
                unset($params['filters']);

                foreach ($filters as &$filter) {
                    if (isset($filter[1]) && is_array($filter[1])) {
                        $filter[1] = ',' . implode(',', $filter[1]);
                    }
                }
                unset($filter);

                $filters = BaseFormRequest::formFilters($filters);

                foreach ($params as $paramName => $values) {
                    if (is_bool($values)) {
                        continue;
                    }
                    $filters[$paramName] = is_array($values) ? (',' . implode(',', $values)) : $values;
                }

                $row['full_url'] = Url::to('/reports/' . $matches['reportId'], $filters);
            }

            if ($row['params'] !== null) {
                $countOfNewLines = substr_count($row['params'], '\n');
                if ($countOfNewLines > 10) {
                    // If to many new lines (probably large list of users) -> split to real new lines for readable output in table
                    $row['params'] = preg_replace('#(?:\\\r)?\\\n#', "\n", $row['params']);
                }
            }
        }

        return $rows;
    }

    protected function total(): int
    {
        $query = (new Query($this->db))
            ->select('COUNT(*)')
            ->from(['l' => Logs::TABLE_NAME])
            ->leftJoin(['e' => Employees::TABLE_NAME], 'e.employee_id = l.employee_id');

        return $this
            ->applyFilters($query)
            ->scalar();
    }

    private function applyFilters(Query $query): Query
    {
        $query
            ->andFilterWhere(['>=', 'l.created_at', $this->from])
            ->andFilterWhere(['<', 'l.created_at', $this->to])
            ->andFilterWhere(['e.email' => Str::explodeText($this->employee)])
            ->andFilterWhere(['l.employee_id' => Str::explodeText($this->employeeId)])
            ->andFilterWhere(['l.ip' => $this->ip])
            ->andFilterWhere(['<', 'LENGTH(l.params::text)', $this->paramsLengthLowerThan])
            ->andFilterWhere(['l.type' => $this->type]);

        if (!empty($this->paramsJsonPath)) {
            $pathAndVars = Str::explodeText($this->paramsJsonPath);
            $query->andWhere('jsonb_path_match(l.params, :path, :vars, true)', ['path' => $pathAndVars[0], 'vars' => $pathAndVars[1] ?? '{}']);
        }

        foreach (Str::explodeText($this->params) as $p) {
            $query->andFilterWhere(['ILIKE', '(l.params::text)', $p]);
        }

        foreach (Str::explodeText($this->paramsNot) as $p) {
            $query->andFilterWhere(['NOT ILIKE', '(l.params::text)', $p]);
        }

        $conditions = [];
        foreach (Str::explodeText($this->url) as $u) {
            $conditions[] = ['ILIKE', 'l.url', $u, null];
        }
        if (!empty($conditions)) {
            $query->andFilterWhere(['OR', ...$conditions]);
        }

        return $query;
    }
}
