<?php

declare(strict_types=1);

namespace app\back\modules\back\games;

use app\back\components\Form;
use app\back\components\validators\CompareValidator;
use app\back\components\validators\IntInArrayValidator;
use app\back\repositories\Games;
use app\back\repositories\GameSources;
use Yiisoft\Db\Connection\ConnectionInterface;

class GamesMergeForm
{
    use Form;

    #[IntInArrayValidator([self::class, 'allowedGames'])]
    #[CompareValidator('game_to', '!==')]
    public int $game_from;
    #[IntInArrayValidator([self::class, 'allowedGames'])]
    public int $game_to;

    public function __construct(
        private readonly ConnectionInterface $db,
        private readonly Games $games
    ) {
    }

    public static function allowedGames(self $form): array
    {
        return $form->games->getNames();
    }

    public function mergeSources(): void
    {
        $this->db->createCommand()->update(
            GameSources::TABLE_NAME,
            ['game_id' => $this->game_to],
            ['game_id' => $this->game_from],
        )->execute();

        $from = $this->games->findOneOr404(['id' => $this->game_from]);
        $from->is_invalid = true;
        $this->games->update($from, ['is_invalid']);
    }
}
