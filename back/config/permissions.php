<?php

declare(strict_types=1);

use app\back\components\Permission;
use app\back\components\RepositoryFactory;
use app\back\modules\api\ApiClients;
use app\back\modules\checks\checks\RulesMap;
use app\back\modules\finance\components\bonus\BonusTypes;
use app\back\modules\user\player\PlayerBlocks;
use app\back\repositories\BonusRestrictions;
use app\back\repositories\Brands;
use app\back\repositories\ChartPanels;
use app\back\repositories\CheckGroups;
use app\back\repositories\S2pProjects;
use app\back\repositories\Settings;
use app\back\repositories\Sites;
use app\back\repositories\TrafficSources;

return [
    Permission::PERM_ALL => [
        'name' => 'All',
        'children' => [
            Permission::PERM_PAGES => [
                'name' => 'All pages',
                'children' => static fn() => Permission::pagesToPermissions(require APP_ROOT . 'back/config/pages.php'),
            ],
            Permission::PERM_PERMISSIONS => [
                'name' => 'All permissions',
                'children' => array_merge(Permission::assocToPermissions([
                    Permission::PERM_USER_STATUS => 'Edit user statuses',
                    Permission::PERM_USER_PM => 'Edit user personal manager',
                    Permission::PERM_CHANGE_CRM_GROUP => 'Edit user CRM group',
                    Permission::PERM_VIEW_SQL => 'View SQL queries',
                    Permission::PERM_WITHDRAW_OVER_LIMIT => 'Withdraw over limit',
                    Permission::PERM_ALLOW_DEP_MANUAL_SUCCESS => 'Allow dep manual success',
                    Permission::PERM_VIEW_STAT_DATA => 'View base users statistic data',
                    Permission::PERM_VIEW_S2P_PAY_CLASSES => 'View S2P Pay classes',
                    Permission::PERM_VIEW_S2P_MIDS => 'View S2P MIDs',
                    Permission::PERM_VIEW_COMMUNICATIONS => 'View communications (chats, sms, etc.)',
                    Permission::PERM_CONTACTS_MANAGE => 'Manage contacts',
                    Permission::PERM_VIEW_CONVERSION_CPA => 'View WP costs in Conversion report',
                    Permission::PERM_VIEW_CONVERSION_LETTERS => 'View Letters tab in Conversion report',
                    Permission::PERM_VIEW_ADJUST_INFO => 'View Adjust Info',
                    Permission::PERM_VIEW_AFF_HIDDEN => 'View Affiliates hidden',
                    Permission::PERM_CONFIRM_CONTACTS => 'Confirm contacts',
                    Permission::PERM_CREATE_BONUS_OFFERS => 'Create bonus offers',
                    Permission::PERM_ADVANCED_BONUS_MODE => 'Advanced bonuses mode',
                    Permission::PERM_CUSTOMER_VIEW => 'View customer',
                    Permission::PERM_USER_BONUS_BALANCE_RESET => 'Reset user bonus balance',
                    Permission::PERM_VIEW_S2P_ROUTING_BRANCH => 'View S2P routing branch',
                    Permission::PERM_TEST_USERS_WITHDRAWAL => 'Withdraw for test user',
                    Permission::PERM_JIRA_APPROVE_LOST_DEPOSIT => 'Jira: approve "Lost Deposit"',
                ]), [
                    Permission::PERM_VIEW_PERSONAL_DATA => [
                        'name' => 'View all personal data',
                        'children' => Permission::assocToPermissions([
                            Permission::PERM_VIEW_REQUISITES => 'View requisites',
                            Permission::PERM_VIEW_EMAILS => 'View emails',
                            Permission::PERM_VIEW_SOCIAL_PROFILES => 'View social profiles',
                            Permission::PERM_VIEW_PHONES => 'View phones',
                            Permission::PERM_LISTEN_PHONE_CALLS => 'Listen phone calls',
                            Permission::PERM_VIEW_TAX_NUMBER => 'View tax number',
                            Permission::PERM_VIEW_ADDRESS => 'View address',
                        ])
                    ],
                    Permission::PERM_NO_BONUS_RESTRICTIONS => [
                        'name' => 'No bonus restrictions',
                        'children' => static fn (RepositoryFactory $repoFactory) => Permission::dict($repoFactory->create(BonusRestrictions::class)),
                    ],
                    Permission::PERM_CHARTS_CREATE => [
                        'name' => 'Create new charts and panels',
                        'children' => Permission::assocToPermissions([
                            Permission::PERM_CHARTS_EDIT => 'Edit charts and panels',
                        ])
                    ],
                    Permission::PERM_CONTACTS_MANAGE => [
                        'name' => 'Manage contacts',
                        'children' => Permission::assocToPermissions([
                            Permission::PERM_CONTACTS_ADD => 'Add new contacts',
                            Permission::PERM_CONTACTS_DELETE => 'Delete contacts',
                            Permission::PERM_CONTACTS_PRIORITY => 'Change contacts priority',
                            Permission::PERM_CONTACTS_PRIVACY => 'Change contacts privacy',
                            Permission::PERM_CONTACTS_SUBSCRIPTIONS => 'Manage subscriptions',
                            Permission::PERM_CONTACTS_EMAIL_STATUS => 'Manage email status',
                        ])
                    ],
                    Permission::PERM_CUSTOMER_EDIT => [
                        'name' => 'Edit customer',
                        'children' => Permission::assocToPermissions([
                            Permission::PERM_CUSTOMER_LINK => 'Link users with customers',
                            Permission::PERM_CUSTOMER_ATTR_ADD => 'Add customer attributes',
                            Permission::PERM_CUSTOMER_PHOTO => "Customer's photo",
                        ]),
                    ],
                    Permission::PERM_VIEW_S2P_TRUST => [
                        'name' => 'View S2P trust',
                        'children' => Permission::assocToPermissions([
                            Permission::PERM_VIEW_S2P_TRUST_GROUPS => 'View S2P trust groups',
                            Permission::PERM_VIEW_S2P_TRUST_SCORE => 'View S2P trust score',
                        ])
                    ],
                    Permission::PERM_USER_MANAGER_TABS => [
                        'name' => 'View all user manager tabs',
                        'children' => Permission::assocToPermissions([
                            'ban' => 'Ban user',
                            'unban' => 'Unban user',
                            'bonus-black-list' => 'Add to bonus black list',
                            'bonus-black-list-remove' => 'Remove from bonus black list',
                            'change-refcode' => 'Change Refcode',
                            'reset-password' => 'Reset user password',
                            'comment' => 'Add comment',
                            'auto-withdrawals' => 'Auto withdrawals',
                        ], Permission::PERM_USER_MANAGER_TAB_PREFIX)
                    ],
                ]),
            ],
            Permission::PERM_SITES => [
                'name' => 'All sites',
                'children' => static fn (RepositoryFactory $repoFactory) => Permission::groupedDict($repoFactory->create(Sites::class)),
            ],
            Permission::PERM_BRANDS => [
                'name' => 'All brands',
                'children' => static fn (RepositoryFactory $repoFactory) => Permission::dict($repoFactory->create(Brands::class)),
            ],
            Permission::PERM_S2P_PROJECTS => [
                'name' => 'All S2P projects',
                'children' => static fn (RepositoryFactory $repoFactory) => Permission::groupedDict($repoFactory->create(S2pProjects::class)),
            ],
            Permission::PERM_CHECKS_GROUPS => [
                'name' => 'All checks groups',
                'children' => static fn (RepositoryFactory $repoFactory) => Permission::dict($repoFactory->create(CheckGroups::class)),
            ],
            Permission::PERM_CHARTS => [
                'name' => 'All charts',
                'children' => static fn (RepositoryFactory $repoFactory) => Permission::dict($repoFactory->create(ChartPanels::class)),
            ],
            Permission::PERM_SETTINGS => [
                'name' => 'All settings',
                'children' => static fn (RepositoryFactory $repoFactory) => Permission::dict($repoFactory->create(Settings::class)),
            ],
            Permission::PERM_TRAFFIC_SOURCES => [
                'name' => 'Traffic sources',
                'children' => static fn (RepositoryFactory $repoFactory) => Permission::dict($repoFactory->create(TrafficSources::class)),
            ],
            Permission::PERM_ASSIGN_ALL_PRIZES => [
                'name' => 'Assign all prizes',
                'children' => static fn () => Permission::dict(new BonusTypes()),
            ],
            Permission::PERM_VIEW_PLAYER => [
                'name' => 'All player blocks',
                'children' => static fn () => Permission::dict(new PlayerBlocks()),
            ],
            Permission::PERM_CHECKS_RULES => [
                'name' => 'All checks rules',
                'children' => static fn () => Permission::dict(new RulesMap()),
            ],
            Permission::PERM_API_CLIENTS => [
                'name' => 'All API clients',
                'children' => static fn () => Permission::dict(new ApiClients()),
            ],
        ],
    ],
];
