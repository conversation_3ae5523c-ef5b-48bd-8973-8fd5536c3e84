<?php

declare(strict_types=1);

use app\back\config\tasks\Res;

return [
    Res::OXR => [
        'rates' => [
            'class' => \app\back\modules\task\actions\RatesTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\RatesRequest::class,
                'url' => 'api/historical/:date.json',
                'aliases' => [
                    'code' => 'code',
                    'rate' => 'rate',
                ],
            ],
        ],
        'rates-latest' => [
            'class' => \app\back\modules\task\actions\RatesLatestTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\RatesRequest::class,
                'url' => 'api/latest.json',
                'aliases' => [
                    'code' => 'code',
                    'rate' => 'rate',
                ],
            ],
        ],
    ],
    Res::MR => [
        'marketing-tids' => [
            'class' => \app\back\modules\task\actions\MarketingTidsTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\MarketingDateRequest::class,
                'url' => 'api/v1/feeds/analytics/:hash/:date.json',
                'aliases' => [
                    'tid' => 'short_refcode',
                    'publisher' => 'publisher_id',
                    'banner' => 'banner_name',
                    'deal' => 'deal_name',
                    'banner_id' => 'banner_id',
                ],
            ],
        ],
    ],
    Res::MRT => [
        'marketing-hosts' => [
            'class' => \app\back\modules\task\actions\import\MarketingHostsTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\BasicAuthRequest::class,
                'url' => 'api/domains',
                'aliases' => [
                    'host' => 'name',
                    'purpose' => 'type',
                    'external_created_at' => 'created_at',
                    'external_updated_at' => 'updated_at',
                    'external_comment' => 'description',
                ],
            ],
        ],
    ],
    Res::WEBMONEY => [
        'update-wmid' => [
            'class' => \app\back\modules\task\actions\update\WmidTask::class,
            'limit' => 500,
            'delay' => 3,
            'request' => [
                'class' => \app\back\modules\task\requests\WebmoneyRequest::class,
                'host' => 'https://passport.webmoney.ru',
                'url' => 'ASP/CertView.asp',
            ],
        ],
    ],
    Res::SYNEF_CLOUD => [
        'employees-mails-public-keys' => [
            'class' => \app\back\modules\task\actions\EmployeesMailsPublicKeysTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\SynefPgpKeysRequest::class,
                'url' => 'pks/lookup',
            ],
        ],
        'employee-mail-public-key' => [
            'class' => \app\back\modules\task\FetchTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\SynefPgpKeysRequest::class,
                'url' => 'pks/lookup',
            ],
        ],
    ],
    Res::ROK => [
        'rokeente-sessions' => [
            'class' => \app\back\modules\task\actions\import\RokeenteSessionsTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\RokeenteRequest::class,
                'url' => 'api/v2/session/updated_at',
                'aliases' => [ // ^\s*(\w+),"?([^"]*?)"?$            '$1' => '$1', // $2
                    'client_created_at' => 'client_time', // Browser Time
                    'server_created_at' => 'real_time', // Date created
                    'sub_1' => 'sub_1', // sub_1
                    'fastfraud_screen_resolution_score' => 'fastfraud_screen_resolution_score', // Screen resolution score
                    'project' => 'project', // Project
                    'session_id' => 'session', // #SessionAF ID
                    'has_dev_tools_open' => 'has_dev_tools_open', // Dev Tools
                    'has_vektor_browser' => 'has_vektor_browser', // VektorT13 Browser
                    'has_incognito' => 'has_incognito', // Incognito
                    'is_iframe' => 'is_iframe', // Iframe
                    'has_apple_pay' => 'has_apple_pay', // ApplePay Enabled
                    'browser_user_agent' => 'browser_user_agent', // Browser User Agent
                    'server_user_agent' => 'server_user_agent', // Server User Agent
                    'operation_system' => 'operation_system', // OS Platform from browser navigator
                    'app_agent' => 'app_agent', // App agent
                    'oscpu' => 'oscpu', // OSCPU
                    'app_code_name' => 'app_code_name', // App Code Name
                    'do_not_track' => 'do_not_track', // Do Not Track
                    'hardware_concurrency' => 'hardware_concurrency', // CPU cores
                    'max_touch_points' => 'max_touch_points', // Max Touch Points
                    'language' => 'language', // Def Lang
                    'languages' => 'languages', // Supp Lang
                    'product_sub' => 'product_sub', // Browser Build
                    'vendor' => 'vendor', // Browser Vendor
                    'screen_width' => 'screen_width', // Screen Width, px
                    'screen_height' => 'screen_height', // Screen Height, px
                    'width' => 'width', // Screen Available Width, px
                    'height' => 'height', // Screen Available Height, px
                    'color_depth' => 'color_depth', // ColorDepth
                    'inner_width' => 'inner_width', // Page Width, px
                    'inner_height' => 'inner_height', // Page Height, px
                    'orientation_type' => 'orientation_type', // Screen Orientation
                    'unmasked_renderer' => 'unmasked_renderer', // WebGL:Renderer
                    'unmasked_vendor' => 'unmasked_vendor', // WebGL:Vendor
                    'asn' => 'asn', // ASN
                    'real_ip_address' => 'real_ip_address', // Real Ip
                    'hostname' => 'hostname', // Hostname
                    'proxy_types' => 'proxy_types', // Ip2location Proxy
                    'proxies' => 'proxies', // Real Ip: Proxy
                    'time_zone' => 'time_zone', // Real Ip: Time Zone
                    'isp' => 'isp', // Real Ip: ISP
                    'organization' => 'organization', // Real Ip: ORG
                    'autonomous_system_organization' => 'autonomous_system_organization', // Real Ip: Anom. ORG
                    'registered_country' => 'registered_country', // Real Ip: ISP reg Country
                    'p0f_detected_os' => 'p0f_detected_os', // p0f: Detected OS
                    'p0f_network_link' => 'p0f_network_link', // p0f: Network Link
                    'canvas_finger_print' => 'canvas_finger_print', // Canvas FingerPrint
                    'webgl_finger_print' => 'webgl_finger_print', // WebGL FingerPrint
                    'http_referer' => 'http_referer', // HTTP referer
                    'promo_engine_http_referrer' => 'promo_engine_http_referrer', // Promo Engine: HTTP referer
                    'browser_plugins' => 'browser_plugins', // JS: Browser Plugins
                    'x_requested_with' => 'x_requested_with', // Requested With
                    'http_via' => 'http_via', // HTTP_VIA
                    'pixel_cookie' => 'pixel_cookie', // Pixel Cookie
                    'referrer_android_app' => 'referrer_android_app', // Referrer Android App
                    'http_x_forwarded_for' => 'http_x_forwarded_for', // HTTP_X_FORWARDED_FOR
                    'blacklists_triggered' => 'blacklists_triggered', // Triggered blacklists JSON
                    'tags' => 'tags', // Tags JSON
                    'ports_opened' => 'ports_opened', // Opened ports JSON
                    'ports_opened_titles' => 'ports_opened_titles', // Opened ports titles JSON
                    'updated_at' => 'updated_at', // "Date updated"
                ],
            ],
        ],
        'rokeente-blacklists' => [
            'class' => \app\back\modules\task\actions\import\RokeenteBlacklistsTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\RokeenteFieldsRequest::class,
                'url' => 'api/fields',
                'aliases' => [
                    'field' => 'field',
                    'name' => 'title'
                ],
            ]
        ],
        'rokeente-tags' => [
            'class' => \app\back\modules\task\actions\import\RokeenteTagsTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\RokeenteTagsRequest::class,
                'url' => 'api/fields',
                'aliases' => [
                    'field' => 'field',
                    'name' => 'title'
                ],
            ]
        ],
    ],
    Res::YS_ML_SEG => [
        'users-segmentation-ys-ml-ggbet' => [
            'class' => \app\back\modules\task\actions\update\UsersSegmentationYsMlTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\YsMlSegmentationRequest::class,
                'url' => 'api/segmentation',
                'curlTimeout' => 600,
                'aliases' => [
                    'user_id' => 'user_id',
                    'cluster' => 'cluster',
                ],
            ],
        ],
    ],
    Res::COIN_API => [
        'rates-crypto' => [
            'class' => \app\back\modules\task\actions\RatesCryptoTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\CoinApiRequest::class,
                'url' => 'v1/exchangerate/USD/:currency?time=:time',
                'keysWarning' => false,
                'objectAsSingleRow' => true,
                'aliases' => [
                    'rate' => 'rate',
                    'code' => 'asset_id_quote',
                ],
            ],
        ],
    ],
    Res::GIN_ML => [
        'update-documents-tags-recognize' => \app\back\modules\task\actions\update\DocumentsTagsRecognizeTask::class,
    ],
    Res::ML_HUB => [
        'ml-hub-infer' => [
            'class' => \app\back\modules\task\FetchTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\MlHubRequest::class,
                'url' => 'api/model/infer/:modelName',
                'curlTimeout' => 1800,
                'path' => 'out',
            ],
        ],
        'ml-hub-train' => [
            'class' => \app\back\modules\task\FetchTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\MlHubRequest::class,
                'url' => 'api/model/train/:modelName',
                'objectAsSingleRow' => true,
                'curlTimeout' => 7200,
                'aliases' => [
                    'message' => 'message',
                ]
            ],
        ],
        'update-users-documents-face-recognize-secondary' => [
            'class' => \app\back\modules\task\actions\update\UsersDocumentsFaceRecognizeSecondaryTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\multiCurl\DeepFaceMultiCurlRequest::class,
                'url' => 'api/model/infer/deepface',
                'curlTimeout' => 100,
                'concurrencyLimit' => 5,
            ],
        ],
        'faces-infer-alternative' => [
            'class' => \app\back\modules\task\FetchTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\multiCurl\DeepFaceMultiCurlRequest::class,
                'url' => 'api/model/infer/deepface',
                'curlTimeout' => 100,
            ],
        ],
    ],
    Res::JIRA => [
        'users-tickets' => [
            'class' => \app\back\modules\task\actions\import\UsersTicketsJiraTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\JiraRequest::class,
                'url' => 'rest/api/2/search?jql=:jql&fields=:fields&expand=:expand',
                'aliases' => [
                    'jira_key' => 'key',
                    'status' => 'fields.status',
                    'type' => 'fields.issuetype',
                    'summary' => 'fields.summary',
                    'assignee' => 'fields.assignee',
                    'priority' => 'fields.priority',
                    'site' => 'fields.customfield_17200',
                    'user_id' => 'fields.customfield_17201',
                    'invoice_id' => 'fields.customfield_12902',
                    'performer' => 'fields.customfield_26104',
                    'created_at' => 'fields.created',
                    'updated_at' => 'fields.updated',
                    'creator' => 'fields.creator',
                    'attachments' => 'fields.attachment',
                    'history' => 'changelog.histories',
                ],
            ],
        ],
        'users-tickets-files' => [
            'class' => \app\back\modules\task\actions\import\UsersTicketsFilesJiraTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\JiraFilesRequest::class,
                'url' => 'secure/attachment/:jiraFileId/:jiraFileName',
            ],
        ],
        'jira-ticket-create' => [
            'class' => \app\back\modules\task\FetchTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\JiraTicketCreateRequest::class,
                'url' => 'rest/api/2/issue',
                'objectAsSingleRow' => true,
                'aliases' => [
                    'jira_key' => 'key',
                ],
            ],
        ],
        'jira-ticket-update-status' => [
            'class' => \app\back\modules\task\FetchTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\JiraTicketUpdateStatusRequest::class,
                'url' => 'rest/api/2/issue/:jiraKey/transitions',
                'aliases' => [],
            ],
        ],
        'jira-ticket-attach-file' => [
            'class' => \app\back\modules\task\FetchTask::class,
            'request' => [
                'class' => \app\back\modules\task\requests\JiraTicketAttachFileRequest::class,
                'url' => 'rest/api/2/issue/:jiraKey/attachments',
                'aliases' => [
                    'jira_file_id' => 'id',
                    'filename' => 'filename'
                ],
            ],
        ],
    ],
];
