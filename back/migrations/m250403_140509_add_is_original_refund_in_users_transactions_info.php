<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250403_140509_add_is_original_refund_in_users_transactions_info extends BaseMigration
{
    private const string TABLE = 'users_transactions_info';
    private const string COLUMN = 'is_original_refund';

    public function safeUp(): void
    {
        $this->db->createCommand()->addColumn(self::TABLE, self::COLUMN, 'boolean DEFAULT false NOT NULL')->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropColumn(self::TABLE, self::COLUMN)->execute();
    }
}
