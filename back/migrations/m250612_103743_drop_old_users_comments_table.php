<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250612_103743_drop_old_users_comments_table extends BaseMigration
{
    public function up(): void
    {
        $this->db->createCommand()
            ->dropTable('users_comments')
            ->execute();
    }

    public function down(): void
    {
        $this->db->createCommand()->createTable('users_comments', [
            'site_id' => 'integer not null',
            'user_id' => 'bigint not null',
            'comment_id' => 'bigint not null',
            'is_visible' => 'boolean default true not null',
            'message' => 'text not null',
            'created_by' => 'varchar(100)',
            'created_at' => 'timestamp(0) default now() not null',
            'archived_at' => 'timestamp(0)',
            'updated_at' => 'timestamp(0)',
        ])->execute();

        $this->db->createCommand()->addPrimaryKey('users_comments', 'users_comments_pkey', ['site_id', 'user_id', 'comment_id'])->execute();
    }
}
