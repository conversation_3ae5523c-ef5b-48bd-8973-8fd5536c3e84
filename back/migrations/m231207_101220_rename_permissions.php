<?php

declare(strict_types=1);

namespace app\back\migrations;

class m231207_101220_rename_permissions extends BaseMigration
{
    private array $redirect = [
        '/reports/remarketing-conversion' => '/reports/remarketing',
        '/reports/start-2-pay-out-percent' => '/reports/out-percent',
        '/reports/hhs-users-games-sessions' => '/reports/games-hhs',
        '/reports/users-games' => '/reports/games',
        '/reports/users-contacts' => '/reports/contacts',
        '/reports/users-cid' => '/reports/cid-users',
        '/reports/marketing-sources-cids' => '/reports/cid-marketing-sources',
        '/reports/start-2-pay-orders' => '/reports/s2p-orders',
        '/reports/start-2-pay-transactions' => '/reports/s2p-transactions',
        '/reports/approve-ratio-s2p' => '/reports/s2p-approve-ratio',
        '/user/statuses' => '/user/statuses-upload',
        '/reports/withdrawals' => '/reports/withdrawals-efficiency',
        '/reports/subscription-conversion' => '/reports/subscriptions',
        '/tools/wa-contacts' => '/tools/whats-app-contacts',
        '/reports/users-tournaments' => '/reports/tournaments',
        '/reports/users-wheel-fortune-statistics' => '/reports/wof-statistics',
        '/reports/users-bonuses-detail' => '/reports/bonuses-detail',
        '/reports/users-bonuses' => '/reports/bonuses',
        '/reports/bonus-users-promo-codes' => '/reports/promo-codes',
        '/reports/users-games-comparison' => '/reports/games-comparison',
        '/back/affiliate-sources' => '/back/remarketing-sources',
    ];

    public function safeUp(): void
    {
        foreach ($this->redirect as $from => $to) {
            $this->db->createCommand()->update(
                'auth_assignments',
                ['item_name' => $to],
                ['item_name' => $from]
            )->execute();

            $this->db->createCommand()->update(
                'auth_roles_permissions',
                ['permission' => $to],
                ['permission' => $from]
            )->execute();
        }
    }

    public function safeDown(): void
    {
        foreach ($this->redirect as $from => $to) {
            $this->db->createCommand()->update(
                'auth_assignments',
                ['item_name' => $from],
                ['item_name' => $to]
            )->execute();

            $this->db->createCommand()->update(
                'auth_roles_permissions',
                ['permission' => $from],
                ['permission' => $to]
            )->execute();
        }
    }
}
