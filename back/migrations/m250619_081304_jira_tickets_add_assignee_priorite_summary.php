<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250619_081304_jira_tickets_add_assignee_priorite_summary extends BaseMigration
{
    private const string JIRA_TABLE = 'users_tickets';

    public function safeUp(): void
    {
        $this->db->createCommand()->addColumn(self::JIRA_TABLE, 'summary', 'varchar(200)')->execute();
        $this->db->createCommand()->addColumn(self::JIRA_TABLE, 'assignee', 'varchar(100)')->execute();
        $this->db->createCommand()->addColumn(self::JIRA_TABLE, 'priority', 'smallint')->execute();
    }

    public function safeDown(): void
    {
        $this->db->createCommand()->dropColumn(self::JIRA_TABLE, 'summary')->execute();
        $this->db->createCommand()->dropColumn(self::JIRA_TABLE, 'assignee')->execute();
        $this->db->createCommand()->dropColumn(self::JIRA_TABLE, 'priority')->execute();
    }
}
