<?php

declare(strict_types=1);

namespace app\back\migrations;

class m250408_211513_withdrawals_comments_null_filling extends BaseMigration
{
    public function up(): void
    {
        $this->sql("DELETE FROM withdrawals_comments WHERE comment IS NULL");

        $this->sql(<<<SQL
DO LANGUAGE plpgsql $$
    DECLARE
        v_min_id integer;
        v_max_id integer;
        v_batch_size integer := 10000;
    BEGIN
        SELECT MIN(id), MAX(id)
        INTO v_min_id, v_max_id
        FROM withdrawals_comments;
    
        WHILE v_min_id IS NOT NULL AND v_min_id <= v_max_id LOOP
            UPDATE withdrawals_comments wc
            SET
                source = 1,
                type = CASE WHEN wc.stats_id IS NOT NULL THEN 2 ELSE 1 END,
                comment_id = CASE WHEN wc.stats_id IS NOT NULL THEN 't' || wc.stats_id END,
                updated_by_email = e.email
            FROM employees e
            WHERE wc.updated_by = e.employee_id
              AND wc.id >= v_min_id
              AND wc.id < v_min_id + v_batch_size;
    
            RAISE INFO 'Updated rows with id between % and %', v_min_id, v_min_id + v_batch_size - 1;
    
            v_min_id := v_min_id + v_batch_size;
        END LOOP;
    END
$$;
SQL
        );
    }

    public function down(): void
    {
    }
}
