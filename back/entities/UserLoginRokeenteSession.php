<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IdValidator;
use app\back\components\validators\StringValidator;

class UserLoginRokeenteSession extends BaseEntity
{
    #[IdValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[StringValidator(0, 50)]
    public string $login_id;
    #[StringValidator(32, 32)]
    public string $session_id;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $updated_at;
}
