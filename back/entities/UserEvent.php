<?php

declare(strict_types=1);

namespace app\back\entities;

use app\back\components\helpers\Arr;
use app\back\components\validators\BigIdValidator;
use app\back\components\validators\DateTimeImmutableValidator;
use app\back\components\validators\IntValidator;
use app\back\components\validators\IpValidator;
use app\back\components\validators\JsonObjectValidator;
use app\back\components\validators\StringValidator;
use app\back\entities\jsonTypes\UserEventInfo;

class UserEvent extends BaseEntity
{
    public const int SOURCE_USER = 1;
    public const int SOURCE_ANALYTICS = 2;
    public const int SOURCE_ADMIN = 3;

    public const array SOURCES = [
        self::SOURCE_USER => 'User',
        self::SOURCE_ANALYTICS => 'Analytics',
        self::SOURCE_ADMIN => 'Admin',
    ];

    public const int EMAIL_CONFIRMATION_REQUEST = 1;
    public const int EMAIL_CONFIRMED = 2;
    public const int PASSWORD_RESET = 3;
    public const int PASSWORD_CHANGE = 4;
    public const int PHONE_CONFIRMATION_REQUEST = 5;
    public const int PHONE_CONFIRMED = 6;
    public const int PASSWORD_RESET_REQUEST = 7;
    public const int EMAIL_CHANGED = 8;
    public const int TWO_FACTOR_REQUEST = 9;
    public const int BONUS_BLACK_LIST = 10;

    public const array EVENT_TYPES = [
        self::EMAIL_CONFIRMATION_REQUEST => 'Email confirmation request',
        self::EMAIL_CONFIRMED => 'Email confirmed',
        self::PASSWORD_RESET => 'Password reset',
        self::PASSWORD_RESET_REQUEST => 'Password reset request',
        self::PASSWORD_CHANGE => 'Password change',
        self::PHONE_CONFIRMATION_REQUEST => 'Phone confirmation request',
        self::PHONE_CONFIRMED => 'Phone confirmed',
        self::EMAIL_CHANGED => 'Email changed',
        self::TWO_FACTOR_REQUEST => 'Two factor request',
        self::BONUS_BLACK_LIST => 'Bonus black list',
    ];

    public int $id;
    #[IntValidator]
    public int $site_id;
    #[BigIdValidator]
    public int $user_id;
    #[IntValidator]
    public int $type;
    #[DateTimeImmutableValidator]
    public ?\DateTimeImmutable $created_at;
    #[IntValidator]
    public ?int $useragent_id;
    #[IpValidator]
    public ?string $ip;
    #[IntValidator]
    public ?int $host_id;
    #[JsonObjectValidator]
    public ?array $info;
    #[StringValidator]
    public ?string $remote_id;
    #[IntValidator]
    public ?int $source;

    public static function getTypeNameById(int $id): ?string
    {
        return self::EVENT_TYPES[$id] ?? null;
    }

    public function getInfoObject(): UserEventInfo
    {
        $result = new UserEventInfo();
        Arr::configure($result, $this->info);

        return $result;
    }
}
