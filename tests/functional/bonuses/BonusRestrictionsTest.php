<?php

declare(strict_types=1);

namespace app\tests\functional\bonuses;

use app\back\components\AccessChecker;
use app\back\components\exceptions\InvalidException;
use app\back\components\Permission;
use app\back\entities\BonusLog;
use app\back\entities\BonusRestriction;
use app\back\entities\Employee;
use app\back\entities\jsonTypes\BonusRestrictionSetting;
use app\back\entities\Site;
use app\back\modules\finance\components\bonus\BonusDictionarySmen;
use app\back\modules\finance\components\bonus\BonusTypes;
use app\back\modules\finance\components\bonus\RestrictionManager;
use app\back\modules\finance\components\bonus\WebBonusForm;
use app\back\repositories\BonusLogs;
use app\back\repositories\BonusRestrictions;
use app\back\repositories\UserWallets;
use app\tests\libs\DbTransactionalUnitTrait;
use app\tests\libs\mock\MockServer;
use app\tests\libs\FakerUnitTrait;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;
use Psr\SimpleCache\CacheInterface;

class BonusRestrictionsTest extends TestCase
{
    use DbTransactionalUnitTrait;
    use FakerUnitTrait;
    use SmenSenderTestTrait;

    private const int SITE_ID = Site::CV;
    private const string REMOTE_BONUS = BonusDictionarySmen::SMEN_MONEY;
    private const string CURRENCY = RestrictionManager::CURRENCY;

    private Employee $employee;

    public function setUp(): void
    {
        $this->container()->get(CacheInterface::class)->clear();
        $this->haveRates();
        $this->employee = $this->haveSystemUser();
    }

    #[DataProvider('bonusDataprovider')]
    public function testBonusRestriction(array $userIds, string $usersString, ?string $bonusSum, ?string $exceptionMessage): void
    {
        $this->setMoneyBonusRestrictionPerDay(1000);
        foreach ($userIds as $userId) {
            $this->haveUserRecord(['user_id' => $userId, 'site_id' => self::SITE_ID]);
            $this->haveRecord(UserWallets::class, [
                'site_id' => self::SITE_ID, 'user_id' => $userId, 'currency' => self::CURRENCY, 'wallet_id' => self::uniqRuntimeId()
            ]);
        }

        MockServer::with(function () use ($usersString, $bonusSum, $exceptionMessage) {
            $form = $this->initForm();
            if ($exceptionMessage !== null) {
                $this->checkException($exceptionMessage);
            }

            $form->validateOrException(self::moneyBonusFormSettings($usersString, $bonusSum));
            $sendError = self::consoleOutputToDebug(fn() => $form->bonusForm->send(BonusLog::TYPE_MANUAL_BONUS, $this->employee, null));
            $this->assertEmpty($sendError);
        }, array_filter([
            [
                $this->bonusListRequest(self::SITE_ID),
                $this->bonusListResponse(),
            ],
            $exceptionMessage === null ? [
                $this->sendAddMoneyBonusRequest((int) $usersString, $bonusSum, $this->employee->email, '8fee9dc877296a4376d0a69b2fd50a54'),
                $this->bonusSuccessResponse(),
            ] : null,
        ]));

        $this->seeRecordWithFields(BonusLogs::class, ['site_id' => self::SITE_ID, 'user_id' => (int) $usersString], [
            'sum' => $bonusSum,
            'currency' => self::CURRENCY,
        ]);
    }

    public static function bonusDataprovider(): array
    {
        $userIds = array_map(static fn() => self::uniqRuntimeId(), range(1, 3));

        $expectedPeriod = date('N') > 5 ? 'weekend' : 'day';
        return [
            [$userIds, (string)$userIds[0], '500.00', null],
            [$userIds, implode("\n", $userIds), '500.00', "sum is greater than your $expectedPeriod limit (1500 > 1000)"],
            [$userIds, implode("\n", array_map(static fn($id) => "$id 500", $userIds)), null, "sum is greater than your $expectedPeriod limit (1500 > 1000)"],
        ];
    }

    private function initForm(): WebBonusForm
    {
        $form = $this->container()->get(WebBonusForm::class);
        $this->container()->get(AccessChecker::class)->clearPermissionTreeAndCache();
        $form->hasUserInForm = false;
        $form->bonusFormMode = WebBonusForm::BONUS_FORM_INIT;
        self::consoleOutputToDebug(static fn() => $form->validateOrException(['siteId' => self::SITE_ID, 'remoteBonusType' => self::REMOTE_BONUS]));
        $form->bonusFormMode = WebBonusForm::BONUS_FORM_VALIDATE;
        return $form;
    }

    private function setMoneyBonusRestrictionPerDay(int $sum): void
    {
        $restrictionSettings = [[
            'bonus_type' => BonusTypes::TYPE_MONEY,
            'period' => BonusRestrictionSetting::PERIOD_DAY,
            'sum' => $sum,
        ]];
        /** @var BonusRestriction $bonusRestriction */
        $bonusRestriction = $this->haveRecord(BonusRestrictions::class, ['name' => 'Test', 'settings' => $restrictionSettings]);
        $this->haveAuthAssigment($this->employee->employee_id, Permission::PERM_BONUS_RESTRICTION_PREFIX . $bonusRestriction->id);
    }

    private function checkException(string $exceptionMessage): void
    {
        $this->expectException(InvalidException::class);
        $this->expectExceptionMessage('{
    "errors": {
        "sum": "' . $exceptionMessage . '"
    }
}');
    }
}
