<?php

declare(strict_types=1);

namespace app\tests\functional\tasks;

use app\back\config\tasks\Res;
use app\back\entities\Site;
use app\back\repositories\Brands;
use app\back\repositories\Visits;
use app\tests\libs\DbTransactionalUnitTrait;

class BufVisitsTaskTest extends BaseActionTestCase
{
    use DbTransactionalUnitTrait;

    private const string CONTENT = <<<DATA
    [
        {
            "project": "szl",
            "date": "2024-09-30",
            "ref_code": "mk_w143215p1864_43765-ios-jerry-SLTZL-RU",
            "platform_name": "iOS",
            "browser_name": "Mobile Safari UI/WKWebView",
            "country": "UA",
            "brand_name": "Slotozal",
            "visits": 10,
            "uniq": 5
        },
        {
            "project": "pid-62",
            "date": "2024-09-30",
            "ref_code": "mk_w143215p1864_43765-ios-jerry-SLTZL-RU",
            "platform_name": "iOS",
            "browser_name": "Mobile Safari UI/WKWebView",
            "country": "UA",
            "brand_name": "Slotozal",
            "visits": 1,
            "uniq": 1
        },
        {
            "project": "hitnspin",
            "date": "2024-09-30",
            "ref_code": "vp_w164736c184521l17172p2075_243",
            "platform_name": "Android",
            "browser_name": "Chrome Mobile",
            "country": "PL",
            "brand_name": "",
            "aff_data": "sub_id=243",
            "visits": 7,
            "uniq": 7
        },
        {
            "project": "7slots",
            "date": "2024-09-30",
            "ref_code": "mb_BQA9RQAA3HAAAPkkAQA.2024-09.30.asoios_221167",
            "platform_name": "Android",
            "browser_name": "Chrome Mobile WebView",
            "country": "TR",
            "brand_name": "BaşarıBet",
            "aff_data": "",
            "visits": 5,
            "uniq": 3
        }
    ]
    DATA;


    public function testBasic(): void
    {
        $this->haveRecords(Brands::class, [
            ['name' => 'Slotozal', 'site_id' => Site::SZL],
            ['name' => 'BaşarıBet', 'site_id' => Site::S7],
        ]);

        $this->runTask('buf-visits', Res::BUF, $this->debugFile(self::CONTENT));

        $d = '2024-09-30';

        $this->seeRecordWithFields(
            Visits::class,
            ['site_id' => Site::SZL, 'date' => $d],
            ['country' => 'UA', 'visits' => 11, 'uniq' => 6],
        );

        $this->seeRecordWithFields(
            Visits::class,
            ['site_id' => Site::HIT, 'date' => $d],
            ['country' => 'PL', 'visits' => 7, 'uniq' => 7]
        );

        $this->seeRecordWithFields(
            Visits::class,
            ['site_id' => Site::S7, 'date' => $d],
            ['country' => 'TR', 'visits' => 5, 'uniq' => 3]
        );
    }
}
