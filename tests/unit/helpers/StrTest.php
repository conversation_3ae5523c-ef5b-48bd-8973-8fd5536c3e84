<?php

declare(strict_types=1);

namespace app\tests\unit\helpers;

use app\back\components\helpers\Str;
use app\back\modules\api\clients\crm\dict\DictBettingBetStatusesMethod;
use app\back\modules\user\player\blocks\payInfo\PayInfoForm;
use app\back\repositories\Users;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

#[CoversClass(Str::class)]
class StrTest extends TestCase
{
    /** @see Str::camel2id */
    #[DataProvider('dataCamel2id')]
    public function testCame2id(string $input, string $expected, ?string $expectedStrict = null): void
    {
        $expectedStrict ??= $expected;
        self::assertEquals($expected, Str::camel2id($input));
        self::assertEquals($expectedStrict, Str::camel2id($input, strict: true));
    }

    public static function dataCamel2id(): array
    {
        return [
            ['test', 'test'],
            ['testString', 'test-string'],
            ['TestString', 'test-string'],
            ['Test/S', 'test/s'],
            ['T/String', 't/string'],
            ['Test/String', 'test/string'],
            ['testA/StringB', 'test-a/string-b'],
            ['/Test/SomeString/', '/test/some-string/'],
            ['Test_StringValue', 'test-string-value'],
            ['Test222String', 'test222-string'],
            ['222test', '222test'],
            ['test222', 'test222'],
            ['testSTRING', 'test-string', 'test-s-t-r-i-n-g'],
            ['TESTString', 'teststring', 't-e-s-t-string'],
        ];
    }

    /** @see Str::camelAndUnderScoreToCamel */
    #[DataProvider('dataCamelAndUnderScoreToCamel')]
    public function testCamelAndUnderScoreToCamel(string $input, string $expected): void
    {
        self::assertEquals($expected, Str::camelAndUnderScoreToCamel($input));
    }

    public static function dataCamelAndUnderScoreToCamel(): array
    {
        return [
            ['refcode_id', 'refcodeId'],
            ['refcode_2id', 'refcode2id'],
            ['refcodeId', 'refcodeId'],
            ['combinedCamel_and_under', 'combinedCamelAndUnder'],
            ['user_id', 'userId'],
            ['s2p', 's2p'],
            ['S2P', 's2P'],
            ['Start2Pay', 'start2Pay'],
            ['start_2_pay', 'start2Pay'],
            ['start2_pay', 'start2Pay'],
        ];
    }

    public function testCleanString(): void
    {
        self::assertEquals('click_id=57e926f8c7252aa9208b4580?_user=47', Str::cleanString(urldecode('click_id=57e926f8c7252aa9208b4580%AE_user%3D47')));
        self::assertEquals("<script>alert(219);</script>", Str::cleanString("<\000script>alert(219);</script\000>"));
    }

    public function testSaveAscii(): void
    {
        $str = "QWERTYUIOP{}ASDFGHJKL:\"ZXCVBNM<>?,./;'[]!@#$%^&*()_+\\|~`1234567890";

        self::assertEquals($str, Str::cleanString($str));
    }

    public function testSaveRu(): void
    {
        $str = "йцукенгшщзхъфывапролджэячсмитьбюё";

        self::assertEquals($str, Str::cleanString($str));
    }

    public function testSaveUa(): void
    {
        $str = "іїє";

        self::assertEquals($str, Str::cleanString($str));
    }

    public function testEmailToLdap(): void
    {
        self::assertEquals('test', Str::emailToLdap('<EMAIL>'), 'valid email');
        self::assertEquals('test', Str::emailToLdap('test'), 'email email without @');
        self::assertEquals('first', Str::emailToLdap('first@second@third'), 'first before @');
        self::assertEquals(null, Str::emailToLdap(null), 'null value');
    }

    public function testClassName2Words(): void
    {
        self::assertEquals('Pay Info', Str::className2Words(PayInfoForm::class, '', 'Form'));
        self::assertEquals('Pay Info', Str::className2Words('PayInfoForm', '', 'Form'));
        self::assertEquals('Users', Str::className2Words(Users::class, '', ''));
        self::assertEquals('Betting Bet Statuses', Str::className2Words(DictBettingBetStatusesMethod::class, 'Dict', 'Method'));
    }

    public function testClassNameWithoutNamespace(): void
    {
        $result = Str::classNameWithoutNamespace('JustClassName');
        $this->assertEquals('JustClassName', $result);

        $result = Str::classNameWithoutNamespace('MyNamespace\MyTestedClass');
        $this->assertEquals('MyTestedClass', $result);

        $result = Str::classNameWithoutNamespace('MyNamespace\MyTestedClass', 'My');
        $this->assertEquals('TestedClass', $result);

        $result = Str::classNameWithoutNamespace('MyNamespace\MyTestedClass', '', 'Class');
        $this->assertEquals('MyTested', $result);

        $result = Str::classNameWithoutNamespace('MyNamespace\MyTestedClass', 'My', 'Class');
        $this->assertEquals('Tested', $result);

        $result = Str::classNameWithoutNamespace('JustClassName', '', '', true);
        $this->assertEquals('Just Class Name', $result);

        $result = Str::classNameWithoutNamespace('JustClaSSName', '', '', true);
        $this->assertEquals('Just Cla S S Name', $result);
    }

    #[DataProvider('dataExtractLanguageFromString')]
    public function testExtractLanguageFromString(?string $input, ?string $expected): void
    {
        self::assertEquals($expected, Str::extractLanguageFromString($input));
    }

    public static function dataExtractLanguageFromString(): array
    {
        return [
            [null, null],
            ['Chromium nie został poprawnie zamknięty.', null],
            ['English(en)', null],
            ['Ukrainian', null],
            ['[{"type":"m"', null],
            ['az-Cyrl', 'az-Cyrl'],
            ['ca-valencia', 'ca'],
            ['de-DE', 'de-DE'],
            ['en-001', 'en-001'],
            ['en-COUNTRYCODE', 'en'],
            ['en-GB-oxendict', 'en-GB'],
            ['en-GB;q=0.8', 'en-GB'],
            ['en-US-u-FW-mon', 'en-US'],
            ['en-US-u-FW-sun-MU-celsius', 'en-US'],
            ['en-US@posix', 'en-US'],
            ['fa-IR-u-NU-latn', 'fa-IR'],
            ['hu-HU,hu,en-US,en', 'hu-HU'],
            ['kk-Cyrl_KZ', 'kk-Cyrl_KZ'],
            ['ua-BY-u-MU-celsius', 'ua-BY'],
            ['ua-UA,ua;q=0.9,ua;q=0.8,en-US;q=0.7,en;q=0.6', 'ua-UA'],
            ['ua-UA-u-FW-mon', 'ua-UA'],
            ['ua-UA.ua;q=0.8', 'ua-UA'],
            ['ua-UA;en-US', 'ua-UA'],
            ['sr-Latn-CS', 'sr-Latn-CS'],
            ['tr', 'tr'],
            ['tzm-Arab-MA', 'tzm-Arab-MA'],
            ['und-bod', 'und-bod'],
            ['use_device_language', null],
            ['zh-CN.UTF-8', 'zh-CN'],
        ];
    }

    #[DataProvider('dataWordsToSnake')]
    public function testWordsToSnake(?string $input, ?string $expected): void
    {
        self::assertEquals($expected, Str::wordsToSnake($input));
    }

    public static function dataWordsToSnake(): array
    {
        return [
            ['Lost Deposit', 'lost_deposit'],
            ['Deposit', 'deposit'],
            ['This Is A Test', 'this_is_a_test'],
            ['  Leading and trailing  ', 'leading_and_trailing'],
            ["Tab\tSeparated Words", 'tab_separated_words'],
            ["Line1\nLine2", 'line1_line2'],
            ["A \t\n B", 'a_b'],
            ['already_snake_case', 'already_snake_case'],
            ['Part-time Job', 'part-time_job'],
            ['Top 10 List', 'top_10_list'],
            ['', ''],
            ['Hello! World?', 'hello!_world?'],
        ];
    }

    #[DataProvider('dataRoundUntilNonZero')]
    public function testRoundUntilNonZero(?string $input, ?string $expected): void
    {
        self::assertEquals($expected, Str::roundUntilNonZero($input));
    }

    public static function dataRoundUntilNonZero(): array
    {
        return [
            [null, null],
            ['0', '0'],
            ['1', '1'],
            ['0.1', '0.1'],
            ['1.1', '1.1'],
            ['asd', 'asd'],
            ['-1', '-1'],
            ['1E5', '1E5'],
            ['1.0', '1'],
            ['1.01', '1.01'],
            ['1.0000', '1'],
            ['1.200', '1.2'],
            ['1.20020', '1.2002'],
            ['1.', '1'],
            ['.1', '.1'],
            ['1000', '1000'],
        ];
    }
}
