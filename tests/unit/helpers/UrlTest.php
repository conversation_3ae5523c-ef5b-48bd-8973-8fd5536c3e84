<?php

declare(strict_types=1);

namespace app\tests\unit\helpers;

use app\back\components\helpers\Url;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

#[CoversClass(Url::class)]
class UrlTest extends TestCase
{
    #[DataProvider('dataTo')]
    public function testTo(array $input, string $expected): void
    {
        self::assertSame($expected, Url::to(...$input));
    }

    public static function dataTo(): array
    {
        return [
            [['/qwe'], '/qwe'],
            [['qwe'], '/qwe'],
            [['qwe/asd'], '/qwe/asd'],
            [['/qwe', ['a' => 'b']], '/qwe?a=b'],
            [['/qwe', ['a' => ' ']], '/qwe?a=+'],
            [['/qwe', [3 => 4]], '/qwe?3=4'],
            [['/qwe', [true => false]], '/qwe?1=0'],
            [['/qwe', ['a' => null]], '/qwe'],
            [['/qwe', ['a' => '']], '/qwe?a='],
            [['/qwe', ['a' => '', 'b' => 'c']], '/qwe?a=&b=c'],
            [['/qwe', ['#' => 'asd']], '/qwe#asd'],
            [['/qwe', ['a' => 'b', 1 => 2, '#' => 'asd']], '/qwe?a=b&1=2#asd'],
        ];
    }

    public function testToAbsolute(): void
    {
        $url = Url::to('qwe', ['a' => 'b'], true);
        self::assertStringStartsWith('http', $url);
        self::assertStringContainsString('://', $url);
        self::assertStringEndsWith('/qwe?a=b', $url);
    }
}
