<?php

declare(strict_types=1);

namespace app\tests\unit\components;

use app\back\components\ResponseCsv;
use PHPUnit\Framework\TestCase;

class ResponseCsvTest extends TestCase
{
    public function testBasic(): void
    {
        $r = new ResponseCsv([
            ['id' => 1, 'title' => 'test1'],
            ['id' => 2, 'title' => 'test2'],
        ]);

        self::assertSame('id;title
1;test1
2;test2
', $r->getContent());
    }

    public function testWithColumns(): void
    {
        $r = new ResponseCsv([
            ['id' => 1, 'title' => 'test1'],
            ['will not be exported' => 'test3', 'id' => 2, 'title' => 'test2'],
            ['id' => 3,],
        ], [
            'id' => 'ID',
            'title' => 'Name',
        ]);

        self::assertSame('ID;Name
1;test1
2;test2
3;
', $r->getContent());
    }

    public function testEnclosure(): void
    {
        $r = new ResponseCsv([
            ['c1' => 'col', 'c2' => '1"2', 'c3' => "\n", 'c4' => 'text with spaces', 'c5' => '\\"qwe""'],
        ]);

        self::assertSame('c1;c2;c3;c4;c5
col;"1""2";"
";"text with spaces";"\""qwe"""""
', $r->getContent());
    }

    public function testSeparator(): void
    {
        $r = new ResponseCsv([
            ['c1' => 'col1', 'c2' => 'col2'],
        ], null, ',');

        self::assertSame('c1,c2
col1,col2
', $r->getContent());
    }

    public function testSkipEmptyCols(): void
    {
        $d = [
            ['id' => 1],
            ['id' => 2],
            ['id' => 3],
        ];
        $c = [
            'id' => 'ID',
            'title' => 'Name',
        ];
        $r = new ResponseCsv($d, $c, skipEmptyCols: false);

        self::assertSame('ID;Name
1;
2;
3;
', $r->getContent());

        $r = new ResponseCsv($d, $c, skipEmptyCols: true);

        self::assertSame('ID
1
2
3
', $r->getContent());
    }

    public function testDecorator(): void
    {
        $r = new ResponseCsv([
            ['id' => 1, 'title' => 'test1'],
            ['id' => 2, 'title' => 'test2'],
        ]);

        $r->setDecorator(static function ($row) {
            $row['id'] .= '-id';
            $row['title'] = 'replaced';
            return $row;
        });

        self::assertSame('id;title
1-id;replaced
2-id;replaced
', $r->getContent());
    }

    public function testIterator(): void
    {
        $iterable = static function () {
            yield ['id' => 1];
            yield ['id' => 2];
        };

        $r = new ResponseCsv($iterable());

        self::assertSame('id
1
2
', $r->getContent());
    }

//    public function testRowsSent(): void
//    {
//        $r = new ResponseCsv([
//            ['id' => 1, 'title' => 'test1'],
//            ['id' => 2, 'title' => 'test2'],
//        ]);
//        $r->sendContent();
//
//        self::assertSame(2, $r->getRowsSent());
//    }

//    public function testAfterSend(): void
//    {
//    }
}
