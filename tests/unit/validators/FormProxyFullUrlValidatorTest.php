<?php

declare(strict_types=1);

namespace app\tests\unit\validators;

use app\back\components\Form;
use app\back\components\validators\DomainValidator;
use app\back\components\validators\ProxyFullUrlValidator;
use PHPUnit\Framework\Attributes\CoversClass;
use PHPUnit\Framework\Attributes\DataProvider;
use PHPUnit\Framework\TestCase;

#[CoversClass(DomainValidator::class)]
class FormProxyFullUrlValidatorTest extends TestCase
{
    #[DataProvider('dataBasic')]
    public function testBasic(mixed $testValue, ?string $expected): void
    {
        $form = new class
        {
            use Form;

            #[ProxyFullUrlValidator]
            public string $value;
        };

        $error = $form->validate(['value' => $testValue]);
        $expected = $expected === null ? $expected : 'Value ' . $expected;
        $this->assertSame(array_filter(['value' => $expected]), $error);
    }

    public static function dataBasic(): array
    {
        return [
            ['socks5://user:pass@127.0.0.1:1080', null],
            ['socks5://user:pass:127.0.0.1:1080', 'invalid url format'],
            ['socks5://user:pass@@127.0.0.1:1080', 'invalid url format'],
            ['user:pass@127.0.0.1:1080', 'invalid url format'],
            ['socks5://:pass@127.0.0.1:1080', 'invalid url format'],
            ['socks5://user:@127.0.0.1:1080', 'invalid url format'],
            ['socks5://user:pass@:1080', 'invalid url format'],
            ['socks5://user:pass@127.0.0.1', 'invalid url format'],
            ['socks5://user pass 127.0.0.1 1080', 'invalid url format'],
            ['just-random-text', 'invalid url format'],
        ];
    }
}
