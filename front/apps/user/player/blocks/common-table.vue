<template>
    <RichTable
        v-bind="richTableProps"
        @reload="onReload"
    />
</template>

<script lang="ts">

import { RichTable } from '@/components'
import { defineComponent, PropType } from 'vue'
import { RichTableType, Values } from '@/types'

interface SiteIdUserId {
    siteId: number;
    userId: number;
}

export default defineComponent({
    components: {
        RichTable,
    },
    props: {
        block: {
            type: String,
            required: true,
        },
        title: {
            type: String,
            required: true,
        },
        siteIdUserId: {
            type: Object as PropType<SiteIdUserId>,
            required: true,
        },
        loadPromise: {
            type: Promise,
            required: true,
        },
        busy: {
            type: Boolean,
            required: true,
        },
        showTotal: {
            type: Boolean,
            default: false,
        },
        showPagination: {
            type: Boolean,
            default: false,
        },
        size: {
            type: String,
            default: undefined,
        },
    },
    emits: ['reload'],
    data () {
        return {
            richTable: {} as RichTableType,
        }
    },

    computed: {
        richTableProps (): RichTableType {
            return Object.assign({
                title: this.title,
                showRefresh: true,
                showReset: true,
                showTotal: this.showTotal,
                showPagination: this.showPagination,
                disabled: this.busy,
            }, this.richTable) as RichTableType
        },
    },

    watch: {
        loadPromise: {
            immediate: true,
            async handler (loadPromise: Promise<RichTableType>) {
                return this.$processRichTableResponse(loadPromise, this.richTable)
            },
        },
    },

    methods: {
        async onReload (params: Values) {
            this.$emit('reload', params)
        },
    },
})
</script>
