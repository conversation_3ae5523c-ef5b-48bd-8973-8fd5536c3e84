<template>
    <RichTable
        v-bind="richTableProps"
        @reload="onReload"
    >
        <template #afterTitle="{refreshCallback}">
            <Popover>
                <button
                    class="btn btn-sm btn-success"
                    type="button"
                    @click="onAddContactOpen"
                >
                    <Icona name="icn-plus" /> Add new contact
                </button>
                <template #content>
                    <FormGrid
                        v-bind="addForm"
                        @change="addForm.values = $event"
                        @submit="onAddContactSubmit($event, refreshCallback)"
                    />
                </template>
            </Popover>
        </template>

        <template #value="{row}: {row: ContactRow}">
            <strong v-if="row.is_primary">{{ row.value }}</strong>
            <template v-else>
                {{ row.value }}
            </template>
        </template>

        <template #confirmed="{row, refreshCallback}: {row: ContactRow, refreshCallback: () => void}">
            <button
                v-if="row.confirmation_allowed"
                type="button"
                class="btn btn-xs me-2"
                :class="row.confirmed_at ? ' btn-primary' : 'btn-secondary'"
                @click="setContactConfirmation(row, refreshCallback)"
            >
                {{ row.confirmed_at ? 'Yes' : 'No' }}
            </button>
            <span>
                {{ row.confirmed_at }}
            </span>
        </template>

        <template #priority="{row, refreshCallback}: {row: ContactRow, refreshCallback: () => void}">
            <button
                type="button"
                class="btn btn-xs"
                :class="row.priority ? ' btn-primary' : 'btn-secondary'"
                @click="setContactPriority(row, refreshCallback)"
            >
                {{ row.priority ? ' Yes' : 'No' }}
            </button>
        </template>

        <template #private="{row, refreshCallback}: {row: ContactRow, refreshCallback: () => void}">
            <button
                type="button"
                class="btn btn-xs"
                :class="row.private ? ' btn-primary' : 'btn-secondary'"
                @click="setContactPrivacy(row, refreshCallback)"
            >
                {{ row.private ? ' Yes' : 'No' }}
            </button>
        </template>

        <template #opt_out="{row, refreshCallback}: {row: ContactRow, refreshCallback: () => void}">
            <button
                type="button"
                class="btn btn-xs"
                :class="row.opt_out ? ' btn-primary' : 'btn-secondary'"
                @click="setContactOptOut(row, refreshCallback)"
            >
                {{ row.opt_out ? ' Yes' : 'No' }}
            </button>
        </template>

        <template #delete="{row, refreshCallback}: {row: ContactRow, refreshCallback: () => void}">
            <button
                type="button"
                class="btn btn-xs btn-danger"
                @click="deleteContact(row, refreshCallback)"
            >
                <Icona name="icn-delete" />
            </button>
        </template>

        <template #status="{row, refreshCallback}: {row: ContactRow, refreshCallback: () => void}">
            <InplaceEdit
                v-if="row.all_statuses && row.all_statuses.length > 0"
                :value="row.status"
                type="select"
                :list="row.all_statuses"
                @submit="setContactStatus(row, $event, refreshCallback)"
            />
            <template v-else>
                {{ row.statusName }}
            </template>
        </template>

        <template #subscriptions="{row, refreshCallback}: {row: ContactRow, refreshCallback: () => void}">
            <BtnGroup
                v-if="row.all_subscriptions && row.all_subscriptions.length > 0"
                :items="row.all_subscriptions"
                :value="row.subscriptions"
                :displayText="false"
                :disabled="row.opt_out"
                size="xs"
                @change="setSubscriptionLevels($event, row, refreshCallback)"
            />
        </template>
    </RichTable>
</template>

<script lang="ts">

import { RichTable, FormGrid, BtnGroup, InplaceEdit, Icona } from '@/components'
import { defineComponent } from 'vue'
import { FormGridType, Values } from '@/types'
import Popover from '@/components/popover.vue'
import CommonTable from './common-table.vue'

interface ContactRow {
    id: number
    value: string
    type_id: number
    is_primary: boolean
    confirmed_at: string
    priority: number
    private: boolean
    opt_out: boolean
    status: number
    statusName: string
    subscriptions: number[]

    all_statuses: {
        id: number
        name: string
    }[]
    all_subscriptions: {
        id: number
        name: string
    }[]
    confirmation_allowed: boolean
}

export default defineComponent({
    components: {
        Icona,
        Popover,
        BtnGroup,
        RichTable,
        FormGrid,
        InplaceEdit,
    },
    mixins: [
        CommonTable,
    ],
    data () {
        return {
            addForm: {} as FormGridType,
        }
    },
    methods: {
        async onAddContactOpen () {
            this.addForm = await this.$fetch('/user/player/contacts/add-form')
        },
        onAddContactSubmit (values: Values, refreshCallback: () => void) {
            this.$processFormResponse(this.$fetch('/user/player/contacts/add', Object.assign({}, values, this.siteIdUserId)), this.addForm).then(() => {
                refreshCallback()
            })
        },

        setContactPriority (row: ContactRow, refreshCallback: () => void) {
            return this.$fetch('/user/player/contacts/set-priority', this.appendSiteUserParams({
                contactId: row.id,
                type: row.type_id,
            })).then(refreshCallback)
        },
        setContactPrivacy (row: ContactRow, refreshCallback: () => void) {
            return this.$fetch('/user/player/contacts/set-privacy', this.appendSiteUserParams({
                contactId: row.id,
            })).then(refreshCallback)
        },
        deleteContact (row: ContactRow, refreshCallback: () => void) {
            if (confirm('Really?')) {
                return this.$fetch('/user/player/contacts/delete', this.appendSiteUserParams({
                    contactId: row.id,
                })).then(refreshCallback)
            }
        },
        setSubscriptionLevels (subscriptions: number[], row: ContactRow, refreshCallback: () => void) {
            return this.$fetch('/user/player/contacts/set-subscription-levels', this.appendSiteUserParams({
                contactId: row.id,
                subscriptions,
            })).then(refreshCallback)
        },

        appendSiteUserParams (params: Values) {
            return Object.assign({}, params, this.siteIdUserId)
        },

        setContactStatus (row: ContactRow, newStatus: number | null, refreshCallback: () => void) {
            return this.$fetch('/user/player/contacts/set-status', this.appendSiteUserParams({
                contactId: row.id,
                status: newStatus,
            })).then(refreshCallback)
        },

        setContactConfirmation (row: ContactRow, refreshCallback: () => void) {
            return this.$fetch('/user/player/contacts/set-confirmation', this.appendSiteUserParams({
                contactId: row.id,
            })).then(refreshCallback)
        },
        setContactOptOut (row: ContactRow, refreshCallback: () => void) {
            return this.$fetch('/user/player/contacts/set-opt-out', this.appendSiteUserParams({
                contactId: row.id,
            })).then(refreshCallback)
        },
    },
})
</script>
