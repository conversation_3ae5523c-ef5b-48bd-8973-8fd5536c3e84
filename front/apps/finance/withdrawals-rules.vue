<template>
    <Card
        :disable-help="true"
    >
        <template #afterHelp="{}">
            <Popover
                title="Steps description"
                position="bottom"
                @open="onStepsDescription"
            >
                <button
                    type="button"
                    class="btn btn-secondary ms-2"
                >
                    <Icona name="icn-question" />
                </button>
                <template #content>
                    <pre>{{ stepsDescription }}</pre>
                </template>
            </Popover>
        </template>

        <RichTable
            v-bind="richTable"
            showRefresh
            :show-total="false"
            :disabled="busy"
            @reload="onReload"
        >
            <template #afterTitle="{refreshCallback}">
                <Popover
                    title="Create new rule"
                    position="right"
                    @open="onAddClick"
                >
                    <button
                        type="button"
                        class="btn btn-sm btn-success"
                    >
                        <Icona name="icn-plus" /> Create new
                    </button>
                    <template #content>
                        <FormGrid
                            v-bind="createForm"
                            @change="createForm.values = $event"
                            @submit="onAddFormSubmit($event, refreshCallback)"
                        />
                    </template>
                </Popover>
            </template>

            <template #active="{row, refreshCallback}">
                <button
                    type="button"
                    class="btn btn-xs"
                    :class="row.active ? ' btn-success' : 'btn-secondary'"
                    @click="onToggleActive(row, refreshCallback)"
                >
                    <Icona
                        :name="row.active ? 'icn-check' : 'icn-pause'"
                    />
                    {{ row.active ? ' Active' : ' Inactive' }}
                </button>
            </template>
            <template #steps="{row, refreshCallback}">
                <InplaceEdit
                    type="textarea"
                    :value="row.steps"
                    @submit="onUpdate({id: row.id, steps: $event, }, refreshCallback)"
                />
            </template>
            <template #description="{row, refreshCallback}">
                <InplaceEdit
                    type="textarea"
                    :value="row.description"
                    @submit="onUpdateDescription({id: row.id, description: $event, }, refreshCallback)"
                />
            </template>
            <template #actions="{row, refreshCallback}">
                <div class="btn-group btn-group-sm">
                    <button
                        type="button"
                        class="btn btn-info"
                        :disabled="busy"
                        @click="onTest(row)"
                    >
                        <Icona name="icn-play" /> Test
                    </button>
                    <button
                        type="button"
                        class="btn btn-danger"
                        :disabled="busy"
                        @click="onDelete(row, refreshCallback)"
                    >
                        <Icona name="icn-delete" /> Delete
                    </button>
                </div>
            </template>
        </RichTable>
        <Popup
            width="large"
            :opened="testResultTable !== null"
            @close="testResultTable = null"
        >
            <EntityTable v-bind="testResultTable" />
        </Popup>
    </Card>
</template>

<script lang="ts">
import { RichTable, FormGrid, Popover, InplaceEdit, Popup, EntityTable, Icona } from '@/components'
import { defineComponent } from 'vue'
import { Values, FormGridType, TableRow, NextWithReload, TableType } from '@/types'
import Card from '@/widgets/card.vue'

export default defineComponent({
    components: {
        Icona,
        Popup,
        Card,
        RichTable,
        FormGrid,
        Popover,
        InplaceEdit,
        EntityTable,
    },

    beforeRouteEnter (to, _from, next: NextWithReload) {
        next(vm => vm.reload(vm.$decodeParams(to.query)))
    },

    data () {
        return {
            richTable: {
                form: {} as FormGridType,
            },
            createForm: {} as FormGridType,
            editForm: {} as FormGridType,
            busy: false,
            helpOpened: false,
            stepsDescription: '',
            testResultTable: null as TableType | null,
        }
    },

    methods: {
        reload (params: Values) {
            this.$processRichTableResponse(this.$fetch(this.$route.path + '/data', params), this.richTable)
        },

        onReload (params: Values) {
            this.$historyReplaceParams(params)
            return this.reload(params)
        },

        async onAddClick () {
            this.createForm = await this.$fetch(this.$route.path + '/create-form')
        },

        onAddFormSubmit (params: Values, refreshCallBack: () => void) {
            return this.$processFormResponse(this.$fetch(this.$route.path + '/create', params), this.createForm).then(() => refreshCallBack())
        },

        onToggleActive (row: TableRow, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/toggle-active', { id: row.id }).then(refreshCallback)
        },

        onTest (row: TableRow) {
            this.busy = true
            this.$fetch(this.$route.path + '/test', { id: row.id })
                .then((resultTable: TableType) => {
                    this.testResultTable = resultTable
                })
                .finally(() => this.busy = false)
        },

        onUpdate (params: Values, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/update-steps', params).then(refreshCallback)
        },

        onUpdateDescription (params: Values, refreshCallback: () => void) {
            this.$fetch(this.$route.path + '/update-description', params).then(refreshCallback)
        },

        onDelete (row: TableRow, refreshCallback: () => void) {
            if (!confirm('Are you sure?')) {
                return
            }

            return this.$fetch(this.$route.path + '/delete', { id: row.id }).then(refreshCallback)
        },

        onStepsDescription () {
            this.$fetch(this.$route.path + '/steps-description').then((data : {descriptions: string[]}) => {
                this.stepsDescription = data.descriptions.join('\n')
            })
        },
    },
})
</script>
