<template>
    <span
        class="an-popover"
        @click="onTargetClick"
    >
        <slot />

        <div
            v-show="opened"
            ref="popover"
            v-mousedown-outside="onClickOutside"
            class="popover fade shadow"
            :class="{[`bs-popover-${placement}`]: true, show: opened}"
            :style="styleObj"
        >
            <div class="popover-header">
                {{ title }}
                <div class="me-auto">
                    <slot name="afterTitle" />
                </div>
                <button
                    type="button"
                    class="btn-close"
                    @click="display(false)"
                />
            </div>
            <div class="popover-body">
                <slot name="content" />
                <button
                    v-if="showCopyButton"
                    type="button"
                    class="btn btn-primary btn-sm ms-2"
                    @click="copyText"
                >
                    <Icona name="icn-copy" /> Copy
                </button>
            </div>
        </div>
    </span>
</template>

<script lang="ts" setup>
import { createPopper, Instance, Placement, auto } from '@popperjs/core'
import { CSSProperties, ref, computed, watch, nextTick, useSlots } from 'vue'
import Icona from '@/components/icona.vue'

const $props = withDefaults(defineProps<{
    title?: string
    isOpened?: boolean
    onHover?: boolean
    onClick?: boolean
    showCopyButton?: boolean
    hideOnOutside?: boolean
    wide?: boolean
    position?: Placement
}>(), {
    title: undefined,
    onClick: true,
    wide: true,
    position: auto,
})

const $emit = defineEmits<{
    open: []
    close: []
}>()

const $slots = useSlots()
const popper = ref<Instance | undefined>(undefined)
const popover = ref<HTMLElement>()
const opened = ref($props.isOpened)
const placement = ref($props.position)

const styleObj = computed(() => ({
    maxWidth: $props.wide ? '600px' : '350px',
}) as CSSProperties)

watch(() => $props.isOpened, (value) => {
    display(value)
})

function display(display: boolean) {
    if (opened.value === display) {
        return
    }
    opened.value = display

    if (display) {
        nextTick(() => {
            const slot = $slots.default
            if (slot === undefined) {
                return
            }

            const refEl = (slot()[0].el || popover.value?.parentElement) as HTMLElement
            popper.value = createPopper(refEl, popover.value as HTMLElement, {
                placement: $props.position,

                onFirstUpdate: ({ placement: updatedPlacement }) => {
                    if (updatedPlacement) {
                        placement.value = updatedPlacement
                    }
                    $emit('open')
                },

                modifiers: [
                    { name: 'offset', options: { offset: [16, 8] } },
                    { name: 'flip' },
                ],
            })
        })
    } else if (popper.value) {
        popper.value.destroy()
        popper.value = undefined
        $emit('close')
    }
}

function onTargetClick(e: MouseEvent) {
    if ($props.onClick && !(popover.value as HTMLElement).contains(e.target as HTMLElement)) {
        display(!opened.value)
    }
}

function onClickOutside() {
    if ($props.hideOnOutside) {
        display(false)
    }
}

async function copyText() {
    const popoverText = ((popover.value as HTMLElement).querySelector('.popover-body') as HTMLElement).innerText;
    //todo use copyToClipboard()
    try {
        await navigator.clipboard.writeText(popoverText);
        // @ts-expect-error Global $notify method
        $notify({ message: 'Text copied successfully', type: 'success' });
    } catch (err) {
        // Works only with HTTPS
        // @ts-expect-error Global $notify method
        $notify({ message: 'Failed to copy text', type: 'error' });
    }
}
</script>
