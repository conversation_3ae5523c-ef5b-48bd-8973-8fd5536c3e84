<template>
    <table
        :class="tableClassesOrDefault"
        v-bind="$attrs"
    >
        <caption
            v-if="showRowsCount"
            class="fst-italic position-absolute"
            style="caption-side: top; font-size: 0.65rem; line-height: 1; top: 0; z-index: 1"
        >
            {{ rows.length }}<br>rows
        </caption>
        <THeader
            v-if="showHeader"
            :columns="columns"
            :sort="sort"
            :disabled="disabled"
            :cellProps="headCellProps"
            :class="{ 'sticky-top': sticky }"
            @sortChanged="sortChanged"
        >
            <template #thead="theadProps">
                <slot
                    name="thead"
                    v-bind="theadProps"
                />
            </template>
        </THeader>

        <tbody>
            <tr v-if="rows.length === 0">
                <td
                    class="text-center"
                    :colspan="columns.length"
                >
                    <em>No data</em>
                </td>
            </tr>
            <RowItem
                v-for="(row, rowIndex) in rows"
                v-bind="rowProps ? rowProps(row) : {}"
                :key="rowKeyName ? (row[rowKeyName] as string) : undefined"
                :row="row"
                :rowIndex="rowIndex"
                :columns="columns"
                :cellProps="rowCellProps"
            >
                <template
                    v-for="slotName in rowsSlotNames"
                    #[slotName]="scope"
                >
                    <slot
                        v-bind="scope"
                        :name="slotName"
                    />
                </template>
            </RowItem>
        </tbody>
    </table>
</template>

<script lang="ts">
import { ScalarOrEmpty, TableRow } from '@/types.ts'
const intlCollator = new Intl.Collator(undefined, { numeric: true, sensitivity: 'base' })
export function universalSort<R extends TableRow> (lRow: R, rRow: R, dir: -1 | 1, colName: string): number {
    let l = lRow[colName] as ScalarOrEmpty
    let r = rRow[colName] as ScalarOrEmpty

    if (typeof l === 'string' && typeof r === 'string') {
        const lReplaced = l.replace(/[ %]/g, '').replace(/,/g, '.')
        const rReplaced = r.replace(/[ %]/g, '').replace(/,/g, '.')

        if (lReplaced !== '' && !isNaN(Number(lReplaced)) && rReplaced !== '' && !isNaN(Number(rReplaced))) {
            l = Number(lReplaced)
            r = Number(rReplaced)

            return dir * (l - r)
        }
    }

    // sort empty values last
    if (l === '' || l === null || l === undefined) {
        return 1
    }

    if (r === '' || r === null || r === undefined) {
        return -1
    }

    l = String(l)
    r = String(r)

    return dir * intlCollator.compare(l, r)
}
</script>

<script lang="ts" setup>
import { computed, CSSProperties, type TdHTMLAttributes } from 'vue'
import THeader from './header.vue'
import RowItem from './row.vue'
import { ClassList, TableColumn, TableStyleValue, TableType } from '@/types'

defineOptions({
    inheritAttrs: false,
})

const $props = withDefaults(defineProps<TableType>(), {
    rowKeyName: undefined,
    tableClasses: undefined,
    columns: () => [],
    sort: undefined,
    sortCallback: undefined,
    data: () => [],
    total: undefined,
    rowProps: undefined,
    showHeader: true,
    showRowsCount: true,
    sticky: false,
})

const $emit = defineEmits<{
    sortChanged: [newSort: string]
}>()

// Computed properties
const tableClassesOrDefault = computed(() => {
    return Object.assign({
        table: true,
        'table-striped': true,
        'text-end': true,
        'table-sm': true,
        'table-entity': true,
        'entity-table-disabled': $props.disabled,
    }, $props.tableClasses || {})
})

const sortColNameAndDir = computed((): {colName: string; dir: -1 | 1} => {
    const s = $props.sort || ''
    const colName = s.endsWith('-') ? s.slice(0, -1) : s
    const dir = s.endsWith('-') ? -1 : 1
    return { colName, dir }
})

const rows = computed((): TableRow[] => {
    if (($props.enableInnerSort && $props.sort) || $props.sortCallback) {
        const { colName, dir } = sortColNameAndDir.value
        const sortCallback = $props.sortCallback || universalSort
        return [...$props.data].sort((l, r) => sortCallback(l, r, dir, colName))
    }
    return $props.data
})

const rowsSlotNames = computed(() => {
    return $props.columns
        .map(({slotName}) => slotName)
        .filter(slotName => slotName)
})

// Methods
function headCellProps (col: TableColumn): TdHTMLAttributes {
    const props: TdHTMLAttributes = {
        class: {} as ClassList,
    }

    if (col.align) {
        props.class[`text-${col.align}`] = true
    }

    if (col.style) {
        props.style = col.style
    }

    return props
}

function rowCellProps (row: TableRow, col: TableColumn): TdHTMLAttributes {
    const props = {
        class: {} as ClassList,
        style: {} as CSSProperties,
    } as TdHTMLAttributes

    const styleKey = `__style__${col.code}`
    if (row[styleKey]) {
        props.style = typeof row[styleKey] === 'string' ? JSON.parse(row[styleKey] as string) : row[styleKey]
    }

    const titleKey = `__title__${col.code}`
    if (row[titleKey]) {
        props.title = row[titleKey] as string
    }

    if (col.align) {
        props.class[`text-${col.align}`] = true
    }

    if (col.longTextBrake) {
        props.class['long-text-brake'] = true
    }

    if (col.nowrap) {
        props.class['text-nowrap'] = true
    }

    if (col.style) {
        props.style = col.style
    }

    const cell = row[col.code] as TableStyleValue
    if (cell && typeof cell === 'object') {
        if (cell.title) {
            props.title = cell.title
        }
        if (cell.style) {
            props.style = Object.assign({}, props.style, cell.style)
        }
    }

    return props
}

function sortChanged(newSort: string): void {
    $emit('sortChanged', newSort)
}
</script>

<style lang="scss">
    table.table-entity {
        position: relative;
        thead th:first-child {
            // for rows count caption
            padding-left: .7rem;
        }
        td.long-text-brake {
            overflow-wrap: anywhere;
        }
    }
</style>
