<template>
    <thead>
        <tr>
            <template v-for="col in sortedColumns">
                <th
                    v-if="!col.hidden"
                    v-bind="cellProps(col)"
                    :key="col.code"
                    :class="{pointer: col.sortable}"
                    class="text-nowrap"
                    @click="sortChanged(col)"
                >
                    {{ col.name === '' ? '[empty]' : col.name }}&nbsp;<Icona
                        v-if="col.sortable"
                        :name="col.sortClass"
                    />
                </th>
            </template>
        </tr>
        <slot
            name="thead"
            :columns="columns"
        />
    </thead>
</template>

<script lang="ts">
import { defineComponent, PropType, TdHTMLAttributes } from 'vue'
import { TableColumn } from '@/types'
import Icona from '@/components/icona.vue'

const sortClasses = {
    asc: 'icn-caret-up',
    desc: 'icn-caret-down',
    none: 'icn-caret-up-down',
}

export default defineComponent({
    components: { Icona },
    props: {
        columns: {
            type: Array as PropType<TableColumn[]>,
            default: () => [] as TableColumn[],
        },

        sort: {
            type: String,
            default: '',
        },

        cellProps: {
            type: Function as PropType<(col: TableColumn) => TdHTMLAttributes>,
            default: () => ({}),
        },
    },

    emits: ['sortChanged'],

    computed: {
        sortedColumns (): (TableColumn & {sortClass: string})[] {
            return this.columns.map(col => {
                const sortClass = (col.code === this.sort) ? 'asc' : ((col.code + '-' === this.sort) ? 'desc' : 'none')
                return { ...col, sortClass: sortClasses[sortClass] }
            })
        },
    },

    methods: {
        sortChanged (col: TableColumn): void {
            if (!col.sortable) {
                return
            }

            let newSort = col.code
            if (this.sort === col.code) {
                newSort = ''
            } else if (this.sort !== col.code + '-') {
                newSort += '-'
            }

            this.$emit('sortChanged', newSort)
        },
    },
})
</script>
