<template>
    <button
        class="btn btn-secondary btn-xs"
        :class="{disabled: loading}"
        @click="loadData"
    >
        <Icona
            name="icn-refresh"
            :class="{'icona-spinner': loading}"
        />
    </button>
</template>

<script lang="ts" setup>
import Icona from '@/components/icona.vue'

defineProps<{
    loading?: boolean
}>()

const $emit = defineEmits<{
    dataRequested: []
}>()

const loadData = () => {
    $emit('dataRequested')
}
</script>
