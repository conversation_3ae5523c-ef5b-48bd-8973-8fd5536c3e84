<template>
    <div>
        <FormGrid
            v-bind="form"
            @change="filtersChanged"
        />

        <div class="row chat-cols">
            <div class="col-sm-6">
                <ul class="timeline">
                    <template v-for="(groupedChats, date) in timeGroups">
                        <li class="time-label">
                            <span>{{ date }}</span>
                        </li>
                        <li>
                            <div
                                v-for="chat in groupedChats"
                                :class="{'bg-secondary': chat.id === selectedChat}"
                                class="timeline-item pointer rounded shadow-sm p-2"
                                @click="chatSelected(chat.id)"
                            >
                                <span class="badge bg-blue">{{ chat.clientMessageCount }}</span>
                                <span class="badge bg-gray ms-1">{{ chat.operatorMessageCount }}</span>

                                {{ chat.topic || 'Chat' }} ({{ chat.time }}, {{ chat.length }})
                            </div>
                        </li>
                    </template>
                </ul>
            </div>
            <div class="col-sm-6">
                <div class="direct-chat">
                    <div
                        v-for="(message, i) in selectedChatMessages"
                        class="direct-chat-msg"
                    >
                        <div class="direct-chat-info clearfix">
                            <span>
                                {{ message.isUser ? 'User' : message.operator || 'Operator' }}
                            </span>

                            <span class="direct-chat-timestamp">
                                {{ i ? message.time : message.dateTime }}
                            </span>
                        </div>

                        <div
                            class="direct-chat-text"
                            :class="{'direct-chat-text-user': message.isUser}"
                        >
                            {{ message.text }}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import FormGrid from './form/grid.vue'
import { ref, computed } from 'vue'
import { FormGridType, ValuesOrFileList } from '@/types'

export interface ChatMessage {
    isUser: boolean
    operator: string
    time: string
    dateTime: string
    text: string
}

export interface Chat {
    id: number
    date: string
    clientMessageCount: number
    operatorMessageCount: number
    topic?: string
    time: string
    length: string
    messages: ChatMessage[]
}

const $props = withDefaults(defineProps<{
    chats?: Chat[]
    form?: FormGridType
}>(), {
    chats: undefined,
    form: undefined,
})

const $emit = defineEmits<{
    filtersChanged: [filters: ValuesOrFileList]
    chatSelected: [chatId: number]
}>()

const selectedChat = ref<number | null>(null)

const selectedChatMessages = computed(() => {
    let messages = [] as ChatMessage[]

    const chat = ($props.chats || []).find((chat: Chat) => chat.id === selectedChat.value)
    if (typeof chat !== 'undefined') {
        messages = chat.messages || []
    }

    return messages
})

const timeGroups = computed(() => {
    const groups = {} as Record<string, Chat[]>

    Object.values($props.chats || []).forEach((chat: Chat) => {
        if (!(chat.date in groups)) {
            groups[chat.date] = []
        }

        groups[chat.date].push(chat)
    })
    return groups
})

const chatSelected = (chatId: number) => {
    selectedChat.value = chatId
    $emit('chatSelected', chatId)
}

const filtersChanged = (filters: ValuesOrFileList) => {
    $emit('filtersChanged', filters)
}
</script>
