<template>
    <div class="gallery-image-buttons">
        <button
            class="position-absolute btn btn-sm rounded-0 top-0 start-0"
            type="button"
            :class="btnClassLeft"
            :title="titleLeft"
            :disabled="!enabledLeft"
            @click.prevent="$emit('clickLeft')"
        >
            <Icona
                :name="iconLeft"
            />
        </button>
        <button
            type="button"
            class="position-absolute btn btn-sm btn-danger rounded-0 top-0 end-0"
            :title="titleRight"
            :disabled="!enabledRight"
            @click.prevent="$emit('clickRight')"
        >
            <Icona name="icn-delete" />
        </button>
    </div>
</template>
<script lang="ts" setup>
import Icona from '@/components/icona.vue'

withDefaults(defineProps<{
    headText?: string
    enabledLeft?: boolean
    enabledRight?: boolean
    titleLeft?: string
    titleRight?: string
    iconLeft?: string
    btnClassLeft?: string
}>(), {
    headText: undefined,
    titleLeft: undefined,
    titleRight: undefined,
    iconLeft: 'icn-check',
    btnClassLeft: 'btn-success',
})

defineEmits<{
    clickLeft: []
    clickRight: []
}>()
</script>
<style lang="scss">
.gallery-image-buttons {
    .head-title {
        opacity: 0.7;
        transform: translate(-50%, 0);
    }
    .btn {
        font-size: 1.2em;
        &:not(:hover) {
            max-width: 45%;
            max-height: 45%;
            overflow: hidden;
        }
    }
}
</style>
