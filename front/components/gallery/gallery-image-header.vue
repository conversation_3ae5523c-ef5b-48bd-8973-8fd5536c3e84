<template>
    <div class="gallery-image-header position-absolute d-inline-block top-0 start-50 px-2 pt-0 pb-1 rounded-bottom fw-bolder bg-body text-body mw-100 text-center">
        <span class="gallery-image-header-short text-nowrap">{{ text }}</span>
        <template v-if="textFull || text">
            <span
                v-for="textItem in [textFull || text].flat()"
                :key="textItem"
                class="gallery-image-header-full text-nowrap"
            >{{ textItem }}<br></span>
        </template>
    </div>
</template>

<script lang="ts" setup>
withDefaults(defineProps<{
    text?: string
    textFull?: string | string[] | null
}>(), {
    text: '',
    textFull: null,
})
</script>

<style lang="scss">
.gallery-image-header {
    opacity: 0.7;
    transform: translate(-50%, 0);
    &:not(:hover) {
        overflow: hidden;
    }
}
.gallery-image-block {
    &:hover .gallery-image-header-short {
        display: none;
    }
    &:not(:hover) .gallery-image-header-full {
        display: none;
    }
}
</style>
