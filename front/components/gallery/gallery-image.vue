<template>
    <div class="gallery-image-block position-relative d-inline-block">
        <a
            :href="doc.src"
            class="gallery-trigger"
        >
            <img
                class="orientation-from-image"
                :alt="title"
                :src="doc.src"
                :title="title"
            >
        </a>

        <div
            v-for="(box, index) in visibleBoxes"
            :key="index"
            class="position-absolute border border-danger detection-box"
            :style="box.style"
        />

        <slot />
    </div>
</template>
<script lang="ts">
import { defineComponent, PropType } from 'vue'

export interface ImageAttributes {
    src: string,
    width?: number,
    height?: number
}

export default defineComponent({
    props: {
        doc: {
            type: Object as PropType<ImageAttributes>,
            required: true,
        },
        boxes: {
            type: Array as PropType<number[][] | null>,
            default: () => null,
        },
        title: {
            type: String,
            default: '',
        },
    },
})
</script>
<style lang="scss">
.gallery-image-block {
    font-size: 0.9em;
    img {
        image-orientation: from-image;
        height: 300px;
    }
    &:hover *:not(:disabled) {
        opacity: 1;
    }
    *:hover {
        z-index: 1;
    }
    .btn:focus {
        box-shadow: none;
    }
}
</style>
