<template>
    <div class="gallery-image-footer">
        <ul class="infos position-absolute fw-bold bottom-0 end-0 m-0 list-unstyled">
            <li
                v-for="(info, i) in infosList"
                :key="i"
                :title="info.title || ''"
                class="text-nowrap text-end"
            >
                <span class="d-inline-block px-2 bg-body text-body" :class="{'colors-inverse': !((infosList.length + i) % 2)}">{{ info.text }}</span>
            </li>
        </ul>
        <div
            v-if="siteUser"
            class="links text-start gallery-image-footer-links position-absolute bg-body fw-bolder d-inline px-2 bottom-0 start-0"
        >
            <div class="gallery-image-icons text-nowrap">
                <router-link
                    v-if="siteId && userId"
                    :to="{name: 'documents', query: {siteId, userId}}"
                    title="Documents"
                    target="_blank"
                >
                    <Icona name="icn-vcard" class="text-body" />
                </router-link>
                <router-link
                    v-if="authUser.can('faceMatching') && (similarId || siteUser)"
                    class="ms-2"
                    :to="{name: 'face-matching', query: {...(similarId ? {faceId: similarId} : {siteUser}), approved: ',yes'}}"
                    title="Face matching"
                    target="_blank"
                >
                    <Icona name="icn-mask-theatre" class="text-primary" />
                </router-link>
                <router-link
                    v-if="authUser.can('faceValidation') && siteUser"
                    class="ms-2"
                    :to="{name: 'face-validation', query: {siteUser}}"
                    title="Face validation"
                    target="_blank"
                >
                    <Icona name="icn-dizzy" class="text-danger" />
                </router-link>
                <router-link
                    v-if="external && siteId && userId"
                    class="ms-2"
                    :to="{name: 'documents-external', query: {siteId, userId}}"
                    title="External document approve"
                    target="_blank"
                >
                    <Icona name="icn-thumb-up" class="text-purple" />
                </router-link>
                <router-link
                    v-if="docId"
                    class="ms-2"
                    :to="{name: 'documents-tags-validation', query: {documentId: docId}}"
                    title="DataBet AI tags"
                    target="_blank"
                >
                    <Icona name="icn-ai" />
                </router-link>
                <br>
            </div>
            <router-link
                :to="{name: 'player', params: {tabId: siteId + '-' + userId}}"
                title="Player"
                target="_blank"
            >
                {{ text || siteUser }}
            </router-link>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import Icona from '@/components/icona.vue'
import { useAuthUser } from '@/utils/auth-user'

interface Info {
    title?: string
    text?: string
}

const $props = defineProps<{
    docId?: number
    siteId?: number
    userId?: number
    siteUser?: string
    text?: string
    similarId?: number
    infos?: string | string[] | Info | Info[]
    external?: boolean
}>()

const authUser = useAuthUser()

const infosList = computed<Info[]>(() => {
    return [$props.infos || []].flat().map((i: string | Info) => {
        if (typeof i === 'string') {
            return { text: i } as Info
        }
        return i as Info
    })
})
</script>

<style lang="scss">
.gallery-image-footer {
    opacity: 0.7;
    .infos:not(:hover) {
        max-width: 50%;
        max-height: 45%;
        overflow: hidden;
    }
    .links:not(:hover) {
        max-width: 90%;
        max-height: 45%;
        overflow: hidden;
    }
    .gallery-image-icons {
        font-size: 1.5em;
    }
}
.gallery-image-block:not(:hover) .gallery-image-icons {
    display: none;
}
</style>
