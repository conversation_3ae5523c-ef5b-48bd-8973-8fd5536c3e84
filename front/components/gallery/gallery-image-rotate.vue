<template>
    <button
        title="Rotate"
        class="image-rotate-left position-absolute btn btn-secondary py-5 d-block"
        :disabled="file.processing"
        :class="{'opacity-50': file.processing}"
        @click.prevent.stop="rotateImageAndEmit(file, 270)"
    >
        <Icona name="icn-arrow-back-up" />
    </button>
    <button
        title="Rotate"
        class="image-rotate-top position-absolute btn btn-secondary px-5 d-block"
        :disabled="file.processing"
        :class="{'opacity-50': file.processing}"
        @click.prevent.stop="rotateImageAndEmit(file, 180)"
    >
        <Icona name="icn-arrow-forward-up" />
    </button>
    <button
        title="Rotate"
        class="image-rotate-right position-absolute btn btn-secondary py-5 d-block"
        :disabled="file.processing"
        :class="{'opacity-50': file.processing}"
        @click.prevent.stop="rotateImageAndEmit(file, 90)"
    >
        <Icona name="icn-undo" />
    </button>
    <button
        title="Mirror"
        class="image-mirror-bottom position-absolute btn btn-secondary px-5 d-block"
        :disabled="file.processing"
        :class="{'opacity-50': file.processing}"
        @click.prevent.stop="rotateImageAndEmit(file, 0, true)"
    >
        <Icona name="icn-arrows-left-right" />
    </button>
</template>

<script lang="ts" setup>
import { ref } from 'vue'
import { useFetch } from '@/utils/fetch'
import { DocFile, Values } from '@/types'
import Icona from '@/components/icona.vue'

const $props = defineProps<{
    file: DocFile
}>()

const $emit = defineEmits<{
    afterRotate: [value: null]
}>()

const $fetch = useFetch()
const rotationsTimestamps = ref<Values>({})

const delay = (delay: number, from: number): Promise<void> => {
    const delayFinal = from + delay - Date.now()
    return new Promise(resolve => delayFinal <= 0 ? resolve() : setTimeout(resolve, delayFinal))
}

const rotateImage = async (file: DocFile, angle: number, flip = false) => {
    file.processing = true

    // await next second to s3 cache update
    await delay(1000, rotationsTimestamps.value[file.filename] as number)

    return $fetch('/finance/documents/rotate', {
        siteId: file.siteId,
        userId: file.userId,
        filename: file.filename,
        angle,
        flip,
    }).then(({ url }: {url: string}) => {
        rotationsTimestamps.value[file.filename] = Date.now()
        file.url = url
    }).finally(() => {
        file.processing = false
    })
}

const rotateImageAndEmit = async (file: DocFile, angle: number, flip = false) => {
    await rotateImage(file, angle, flip)
    $emit('afterRotate', null)
}
</script>

<style lang="scss">
.image-rotate-left {
    top: 50%;
    left: 0;
    transform: translate(0, -50%);
}
.image-rotate-top {
    top: 0;
    left: 50%;
    transform: translate(-50%);
}
.image-rotate-right {
    top: 50%;
    right: 0;
    transform: translate(0, -50%);
}
.image-mirror-bottom {
    bottom: 0;
    left: 50%;
    transform: translate(-50%);
}
.image-rotate-left, .image-rotate-top, .image-rotate-right, .image-mirror-bottom {
    opacity: 0.7;
}
</style>
