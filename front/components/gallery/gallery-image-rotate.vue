<template>
    <button
        v-for="button in rotateButtons"
        :key="button.position"
        :title="button.title"
        :class="[
            `image-${button.position}`,
            'position-absolute btn btn-secondary d-block',
            button.padding,
            { 'opacity-50': file.processing }
        ]"
        :disabled="file.processing"
        @click.prevent.stop="rotateImageAndEmit(file, button.angle, button.flip)"
    >
        <Icona :name="button.icon" />
    </button>
</template>

<script lang="ts" setup>
import { ref, computed } from 'vue'
import { useFetch } from '@/utils/fetch'
import { DocFile } from '@/types'
import Icona from '@/components/icona.vue'

interface RotateButton {
    position: string
    title: string
    padding: string
    angle: number
    flip: boolean
    icon: string
}

defineProps<{
    file: DocFile
}>()

const $emit = defineEmits<{
    afterRotate: [value: null]
}>()

const $fetch = useFetch()
const rotationsTimestamps = ref<Record<string, number>>({})

const rotateButtons = computed<RotateButton[]>(() => [
    {
        position: 'rotate-left',
        title: 'Rotate Left',
        padding: 'py-5',
        angle: 270,
        flip: false,
        icon: 'icn-arrow-back-up',
    },
    {
        position: 'rotate-top',
        title: 'Rotate 180°',
        padding: 'px-5',
        angle: 180,
        flip: false,
        icon: 'icn-arrow-forward-up',
    },
    {
        position: 'rotate-right',
        title: 'Rotate Right',
        padding: 'py-5',
        angle: 90,
        flip: false,
        icon: 'icn-undo',
    },
    {
        position: 'mirror-bottom',
        title: 'Mirror Horizontally',
        padding: 'px-5',
        angle: 0,
        flip: true,
        icon: 'icn-arrows-left-right',
    },
])

function delay(delayMs: number, fromTimestamp: number): Promise<void> {
    const remainingDelay = fromTimestamp + delayMs - Date.now()
    return new Promise(resolve => remainingDelay <= 0 ? resolve() : setTimeout(resolve, remainingDelay))
}

async function rotateImageAndEmit(file: DocFile, angle: number, flip = false): Promise<void> {
    file.processing = true

    try {
        // Wait for S3 cache update
        const lastRotationTime = rotationsTimestamps.value[file.filename]
        await delay(1000, lastRotationTime)

        const response = await $fetch<{ url: string }>('/finance/documents/rotate', {
            siteId: file.siteId,
            userId: file.userId,
            filename: file.filename,
            angle,
            flip,
        })

        rotationsTimestamps.value[file.filename] = Date.now()
        file.url = response.url
    } finally {
        file.processing = false
    }

    $emit('afterRotate', null)
}
</script>

<style lang="scss">
.image-rotate-left {
    top: 50%;
    left: 0;
    transform: translateY(-50%);
}
.image-rotate-top {
    top: 0;
    left: 50%;
    transform: translateX(-50%);
}
.image-rotate-right {
    top: 50%;
    right: 0;
    transform: translateY(-50%);
}
.image-mirror-bottom {
    bottom: 0;
    left: 50%;
    transform: translateX(-50%);
}
.image-rotate-left, .image-rotate-top, .image-rotate-right, .image-mirror-bottom {
    opacity: 0.7;
}
</style>
