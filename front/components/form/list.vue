<template>
    <div ref="form-list" class="from-list">
        <div
            v-for="(formValuesBlock, blockIndex) in formBlocks"
            v-show="sortableActiveSet != undefined || formValuesBlock.blockSets.filter(s => s.values.length).length"
            :key="blockIndex"
            :class="blockIndex > 0 ? ['border', 'block-or', `block-${colorNameByBlockIndex(blockIndex)}`] : ['block-zero']"
            class="rounded-3 mb-3"
        >
            <span v-if="blockIndex > 0 && sortableActiveSet !== undefined" class="block-or-title">
                Or conditions
            </span>
            <div
                v-for="(valuesSet, setIndex) in formValuesBlock.blockSets"
                v-show="valuesSet.values.length || setIndex === sortableActiveSet"
                :key="`${blockIndex}-${setIndex}`"
                class="block-body"
            >
                {{ valuesSet.title }}
                <div
                    ref="sortable-drop-zones"
                    class="row mt-1 drop-zone"
                    :class="{'active-drop': setIndex === sortableActiveSet}"
                    :data-set-index="setIndex"
                    :data-block-index="blockIndex"
                >
                    <div
                        v-for="(element, valueIndex) in valuesSet.values"
                        :key="element.key"
                        class="hidden-on-form-params"
                        :data-name="element.name"
                        :class="`mb-3 col-md-${inputsByName[element.name].width}`"
                    >
                        <button
                            type="button"
                            class="btn btn-xs btn-secondary me-1"
                            :disabled="disabled"
                            title="Delete filter"
                            @click="onDelete({blockIndex, setIndex, valueIndex})"
                        >
                            <Icona name="icn-delete" />
                        </button>
                        <label
                            class="label-sm me-1 cursor-draggable"
                            :for="`el-${blockIndex}-${setIndex}-${valueIndex}`"
                        >{{ inputsByName[element.name].title }}</label>
                        <FormOperator
                            v-if="inputsByName[element.name].operators"
                            :operators="inputsByName[element.name].operators"
                            :value="element.operator"
                            :disabled="disabled"
                            @input="onChangeOperator({blockIndex, setIndex, valueIndex}, $event)"
                        />
                        <component
                            :is="`el-${inputsByName[element.name].operatorComponent?.type}`"
                            v-if="inputsByName[element.name].operatorComponent"
                            v-bind="inputsByName[element.name].operatorComponent"
                            :id="`el-${blockIndex}-${setIndex}-${valueIndex}-o`"
                            :enabled="!disabled"
                            :value="element.value"
                            @change="onChange({blockIndex, setIndex, valueIndex}, $event)"
                        />

                        <component
                            :is="`el-${inputsByName[element.name].props?.type}`"
                            v-bind="inputsByName[element.name].props"
                            :id="`el-${blockIndex}-${setIndex}-${valueIndex}`"
                            :ref="`el-${blockIndex}-${setIndex}-${valueIndex}`"
                            :value="element.value"
                            :isInvalid="element.error !== undefined"
                            :enabled="!disabled"
                            @change="onChange({blockIndex, setIndex, valueIndex}, $event)"
                            @liveSearch="onLiveSearch({name: element.name, params: $event, url: inputsByName[element.name].props?.liveSearchUrl})"
                            @submit="onSubmit"
                            @reset="onReset"
                        />
                        <FormError :error="element.error" />
                        <small
                            v-if="inputsByName[element.name].props?.hint"
                            class="form-text"
                        >
                            {{ inputsByName[element.name].props?.hint }}
                        </small>
                    </div>
                </div>
                <hr class="set-hr">
            </div>
        </div>
    </div>
</template>

<script lang="ts" setup>
import { watch, onUnmounted, nextTick } from 'vue'
import {
    ElCheckbox,
    ElDate,
    ElFile, ElGroupSelect,
    ElList, ElMdEditor, ElMultipleSubmit,
    ElRadioList, ElButton, ElSelect,
    ElTextArea,
    ElTextInput,
    ElTextInputLiveSearch,
    ElTextStatic,
} from '@/components/form/types'
import FormError from './error.vue'
import FormOperator from './operator.vue'
import Icona from '@/components/icona.vue'
import Sortable, { SortableEvent } from 'sortablejs'
import { FormValueIndex, FormInput, FormBlock, Value, Values, FormValue, SortableFormEvent } from '@/types.ts'
import { toInteger } from 'lodash'

const sortInstances = [] as Sortable[]
const bootstrapColorVarNames = [
    'yellow',
    'pink',
    'orange',
    'teal',
]

const $props = withDefaults(defineProps<{
    disabled?: boolean
    formBlocks?: FormBlock[]
    inputsByName: Record<string, FormInput>
    dragAndDrop?: boolean
    sortableActiveSet?: number
    focusOnFormValue?: FormValue
}>(), {
    disabled: false,
    formBlocks: () => [],
    dragAndDrop: false,
    sortableActiveSet: undefined,
    focusOnFormValue: undefined,
})

const $emit = defineEmits<{
    submit: []
    reset: []
    delete: [valueIndex: FormValueIndex]
    changeOperator: [data: { valueIndex: FormValueIndex, operator: string }]
    liveSearch: [data: { name: string, params: Values, url?: string }]
    change: [data: { valueIndex: FormValueIndex, value: Value }]
    sortableStart: [event: SortableFormEvent]
    sortableEnd: [event: SortableFormEvent]
    sortableAdd: [event: SortableFormEvent]
    sortableUpdate: [event: SortableFormEvent]
}>()

    watch: {
        focusOnFormValue: {
            flush: 'post',
            handler() {
                this.onFocusOnValueChange()
            },
        },
        formBlocks: {
            flush: 'post',
            deep: true,
            handler () {
                if (this.dragAndDrop) {
                    this.sortableDestroy()
                    this.sortableInit()
                }
            },
        },
    },
    unmounted () {
        if (this.dragAndDrop) {
            this.sortableDestroy()
        }
    },

    methods: {
        onChange (valueIndex: FormValueIndex, value: Value) {
            this.$emit('change', { valueIndex, value })
        },
        onLiveSearch ({ name, params, url }: { name: string, params: Values, url?: string }) {
            this.$emit('liveSearch', { name, params, url })
        },
        onChangeOperator (valueIndex: FormValueIndex, operator: string) {
            this.$emit('changeOperator', { valueIndex, operator })
        },
        onDelete (valueIndex: FormValueIndex) {
            this.$emit('delete', valueIndex)
        },
        onReset () {
            this.$emit('reset')
        },
        onSubmit () {
            this.$emit('submit')
        },
        onFocusOnValueChange () {
            if (!this.focusOnFormValue) {
                return
            }

            this.formBlocks.some((b, blockIndex) => b.blockSets.some((s, setIndex) => s.values.some((formValue, valueIndex) => {
                if (formValue.key === this.focusOnFormValue?.key) {
                    const el = (this.$refs[`el-${blockIndex}-${setIndex}-${valueIndex}`] as { $el: HTMLElement }[])[0]?.$el

                    if (el instanceof HTMLDivElement) {
                        (el.querySelector('textarea') ?? el.querySelector('input'))?.focus()
                    } else if (el.nextSibling && typeof (el.nextSibling as HTMLElement).click === 'function') {
                        (el.nextSibling as HTMLElement).click()
                    } else {
                        el.focus()
                    }
                    return true
                }
            })));
        },
        sortableInit () {
            ((this.$refs['sortable-drop-zones'] || []) as HTMLElement[]).forEach(dropZone => {
                sortInstances.push(Sortable.create(dropZone, {
                    group: 'report-filters-set-' + dropZone.dataset.setIndex,
                    animation: 150,
                    handle: 'label',
                    onStart: (evt: SortableEvent) => this.emitSortableEvent('Start', evt),
                    onEnd: (evt: SortableEvent) => this.emitSortableEvent('End', evt),
                    onAdd: (evt: SortableEvent) => {
                        evt.item.remove()
                        this.emitSortableEvent(this.isListDropNode(evt.from) ? 'Update' : 'Add', evt)
                    },
                    onUpdate: (evt: SortableEvent) => this.emitSortableEvent('Update', evt),
                    onRemove: (evt: SortableEvent) => !this.isListDropNode(evt.to) && this.onDelete(this.datasetToValueAddress(evt.from.dataset, evt.oldIndex as number)),
                }))
            })
        },
        datasetToValueAddress(dataset: DOMStringMap, valueIndex: number) {
            return {
                blockIndex: toInteger(dataset.blockIndex),
                setIndex: toInteger(dataset.setIndex),
                valueIndex,
            }
        },
        emitSortableEvent (type: 'Start' | 'Update' | 'Add' | 'End', evt: SortableEvent) {
            this.$emit(`sortable${type}`, {
                valueName: evt.item.dataset.name,
                from: this.datasetToValueAddress(evt.from.dataset, evt.oldIndex as number),
                to: this.datasetToValueAddress(evt.to.dataset, evt.newIndex as number),
            } as SortableFormEvent)
        },
        sortableDestroy () {
            sortInstances.forEach(inst => inst.destroy())
            sortInstances.splice(0)
        },
        isListDropNode (node: HTMLElement) {
            return !!node.dataset.blockIndex
        },
        colorNameByBlockIndex (blockIndex: number) {
            return bootstrapColorVarNames[blockIndex % bootstrapColorVarNames.length || 0]
        },
    },
})
</script>
<style lang="scss">
.from-list {
    .hidden-on-form-list {
        display: none;
    }

    .block-yellow {
        --bs-block-color: var(--bs-yellow-rgb)
    }

    .block-pink {
        --bs-block-color: var(--bs-pink-rgb)
    }

    .block-orange {
        --bs-block-color: var(--bs-orange-rgb)
    }

    .block-teal {
        --bs-block-color: var(--bs-teal-rgb)
    }

    .block-zero .drop-zone.active-drop {
        border: var(--bs-border-width) var(--bs-border-style) var(--bs-border-color) !important;
        border-radius: var(--bs-border-radius-lg);
        background-color: rgba(var(--bs-tertiary-bg-rgb), 1) !important;
    }

    .block-or {
        margin-left: -5px;
        margin-right: -5px;
        padding-top: 5px;
        padding-left: 5px;
        padding-right: 5px;
        position: relative;

        &.border {
            border-color: rgba(var(--bs-block-color), 0.3) !important;
        }

        .block-or-title {
            position: absolute;
            top: 4px;
            left: 50%;
            transform: translate(-50%);
            color: rgba(var(--bs-block-color), 0.5) !important
        }

        .drop-zone.active-drop {
            background-color: rgba(var(--bs-block-color), 0.1) !important;
            border-color: rgba(var(--bs-block-color), 0.5) !important;
        }
    }

    .active-drop {
        min-height: 3em;

        &:not(.block-zero) {
            border-top: 1px solid;
            border-bottom: 1px solid;
        }
    }

    .block-body:nth-last-child(-n + 1 of :not([style*="display: none"])) {
        .active-drop {
            border-bottom: none;
        }

        .set-hr {
            display: none;
        }
    }
}
</style>
