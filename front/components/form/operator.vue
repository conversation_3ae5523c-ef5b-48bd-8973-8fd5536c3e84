<template>
    <button
        v-if="operator"
        type="button"
        class="btn btn-xs btn-secondary"
        @click="onClick"
    >
        {{ operator.name }}
    </button>
</template>

<script lang="ts" setup>
import { computed } from 'vue'
import { Item } from '@/types.ts'

const $props = withDefaults(defineProps<{
    value?: string
    operators?: Item<string>[]
    disabled?: boolean
}>(), {
    value: undefined,
    operators: () => [],
})

const $emit = defineEmits<{
    input: [value: string]
}>()

const operator = computed(() => {
    if ($props.operators.length === 0) {
        return null
    }

    const foundOperator = $props.operators.find(o => o.id === $props.value)

    if (!foundOperator) {
        return $props.operators[0]
    }

    return foundOperator
})

function onClick() {
    let operatorIndex = $props.operators.findIndex(o => o.id === $props.value)

    if (operatorIndex === -1) {
        operatorIndex = 1
    } else if (operatorIndex === $props.operators.length - 1) {
        // If last operator in list
        operatorIndex = 0
    } else {
        operatorIndex++
    }

    $emit('input', $props.operators[operatorIndex].id)
}
</script>
