<template>
    <div
        class="dropdown p-0 border-0"
        :class="{'is-invalid': isInvalid}"
    >
        <div
            class="position-relative"
            @focusout="onFocus('input', false)"
            @focusin="onFocus('input', true)"
            @keyup.esc="autocompleteItems = []"
            @keydown.up="onKeyNavigation"
            @keydown.down="onKeyNavigation"
        >
            <component
                :is="`el-${inputType}`"
                ref="input"
                :class="inputClass"
                :value="value"
                v-bind="{isInvalid, ...inputProps}"
                autocomplete="off"
                @input="onInputText"
                @submit="$emit('submit')"
                @change="$emit('change', $event)"
            />
            <Icona
                :name="loadingIconName"
                :class="loadingAnimationClass"
                class="position-absolute"
                style="right: 0.5rem; top: 0.6rem"
            />
        </div>
        <div
            v-if="autocompleteVisible && autocompleteItems.length"
            ref="autocomplete"
            class="dropdown-menu pre-scrollable p-0 d-inline-block"
            @focusout="onFocus('autocomplete', false)"
            @focusin="onFocus('autocomplete', true)"
        >
            <a
                v-for="item in autocompleteItems"
                class="dropdown-item py-1 px-2"
                href="#"
                @mouseup.prevent="chooseAutocomplete(item.name)"
                @keydown.enter.prevent="() => null /* disable send form on choose */"
                @keyup.enter.prevent="chooseAutocomplete(item.name)"
                @keyup.esc="setFocus(); autocompleteItems = []"
                @keydown.up.prevent="onKeyNavigation"
                @keydown.down.prevent="onKeyNavigation"
            >{{ item.name }}</a>
        </div>
    </div>
</template>
<script lang="ts">
import ElTextArea from './text-area.vue'
import ElTextInput from './text-input.vue'
import { defineComponent } from 'vue'

export default defineComponent({
    components: { ElTextArea, ElTextInput },
})
</script>

<script lang="ts" setup>
import { ref, computed, nextTick, useTemplateRef } from 'vue'
import Icona from '@/components/icona.vue'
import { Item } from '@/types.ts'
import { useFetch } from '@/utils/fetch.ts'

defineOptions({
    inheritAttrs: false,
})

const $props = withDefaults(defineProps<{
    id: string
    enabled?: boolean
    value?: string | number
    liveSearchUrl: string
    inputType?: string
    inputClass?: string
    isInvalid?: boolean
}>(), {
    enabled: true,
    value: undefined,
    inputType: 'text-input',
    inputClass: undefined,
})

const $emit = defineEmits<{
    change: [value: string]
    submit: []
    input: [value: string, event: KeyboardEvent]
}>()

const $fetch = useFetch()

// Template refs
const input = useTemplateRef<InstanceType<typeof ElTextArea | typeof ElTextInput>>('input')
const autocomplete = useTemplateRef<HTMLDivElement>('autocomplete')

// Reactive data
const inputRow = ref(0)
const inputLines = ref<string[]>([])
const focused = ref({
    input: false,
    autocomplete: false,
})
const autocompleteVisible = ref(false)
const autocompleteItems = ref<Item[]>([])
const liveSearchTimeout = ref(0)
const loading = ref(false)
const awaiting = ref(false)
// Computed properties
const loadingIconName = computed(() => {
    return (loading.value && 'icn-spinner') ||
        (awaiting.value && 'icn-spinner') ||
        (autocompleteItems.value.length && 'icn-caret-down') ||
        'icn-no-icon'
})

const loadingAnimationClass = computed(() => {
    return (loading.value && 'icona-spinner') ||
        (awaiting.value && 'text-body-secondary') ||
        (autocompleteItems.value.length && 'text-body-secondary') ||
        'display-none'
})

const isMultiline = computed(() => {
    return $props.inputType === 'text-area'
})

const inputProps = computed(() => {
    return {
        id: $props.id,
        enabled: $props.enabled,
    }
})
// Methods
function onInputText(value: string, event: KeyboardEvent) {
    $emit('input', value, event)
    if (isMultiline.value) {
        const inputElement = event.target as HTMLTextAreaElement
        inputLines.value = inputElement.value.split('\n')
        let cursor = 0
        for (inputRow.value = 0; inputRow.value < inputLines.value.length; inputRow.value++) {
            cursor += inputLines.value[inputRow.value].length
            if (cursor >= inputElement.selectionStart) {
                break
            }
            cursor++ // line break
        }
        value = inputLines.value[inputRow.value]
    }

    autocompleteValue(value)
}
function autocompleteValue(value: string) {
    clearTimeout(liveSearchTimeout.value)
    loading.value = false
    if (value.length === 0) {
        awaiting.value = false
        autocompleteItems.value = []
        return
    }
    awaiting.value = true
    liveSearchTimeout.value = setTimeout(() => {
        awaiting.value = false
        loading.value = true
        $fetch($props.liveSearchUrl, { q: value })
            .then((data: Item[]) => {
                autocompleteVisible.value = true
                autocompleteItems.value = filterExists(data)
            })
            .finally(() => loading.value = false)
    }, 500) as unknown as number
}
function chooseAutocomplete(value: string) {
    if (isMultiline.value) {
        const prev = inputLines.value.splice(inputRow.value, 1, value)[0]
        inputRow.value++
        autocompleteItems.value = filterExists(autocompleteItems.value)
        if (autocompleteItems.value.length) {
            inputLines.value.splice(inputRow.value, 0, prev)
        } else {
            setFocus()
        }

        const cursor: number = inputLines.value.slice(0, inputRow.value + 1).join('\n').length
        value = inputLines.value.join('\n')
        nextTick(() => input.value?.$el.setSelectionRange(cursor, cursor))
    } else {
        autocompleteVisible.value = false
        setFocus()
    }

    $emit('change', value)
}

function setFocus() {
    input.value?.$el.focus()
}

function onFocus(item: keyof typeof focused.value, value: boolean) {
    focused.value[item] = value
    setTimeout(() => autocompleteVisible.value = focused.value.input || focused.value.autocomplete, 100)
}

function onKeyNavigation(event: KeyboardEvent) {
    if (autocompleteVisible.value) {
        const autocompleteElement = autocomplete.value
        const items = [input.value?.$el.value, ...((autocompleteElement?.children || []))] as HTMLElement[]
        const focusOffset = { ArrowDown: 1, ArrowUp: -1 }[event.key]
        const target = focusOffset ? items.indexOf(event.target as HTMLElement) + focusOffset : undefined
        if (target && items[target]) {
            items[target].focus()
        }
    }
    return true
}

function filterExists(values: Item[]): Item[] {
    return values.filter(item => !inputLines.value.includes(item.name))
}
</script>
