<template>
    <div
        class="dropdown p-0 border-0"
        :class="{'is-invalid': isInvalid}"
    >
        <div
            class="position-relative"
            @focusout="focus('input', false)"
            @focusin="focus('input', true)"
            @keyup.esc="autocompleteItems = []"
            @keydown.up="navigation"
            @keydown.down="navigation"
        >
            <component
                :is="`el-${inputType}`"
                ref="input"
                :class="inputClass"
                :value="value"
                v-bind="{isInvalid, ...inputProps}"
                autocomplete="off"
                @input="inputText"
                @submit="$emit('submit')"
                @change="$emit('change', $event)"
            />
            <Icona
                :name="loadingIconName"
                :class="loadingAnimationClass"
                class="position-absolute"
                style="right: 0.5rem; top: 0.6rem"
            />
        </div>
        <div
            v-if="autocompleteVisible && autocompleteItems.length"
            ref="autocomplete"
            class="dropdown-menu pre-scrollable p-0 d-inline-block"
            @focusout="focus('autocomplete', false)"
            @focusin="focus('autocomplete', true)"
        >
            <a
                v-for="item in autocompleteItems"
                class="dropdown-item py-1 px-2"
                href="#"
                @mouseup.prevent="chooseAutocomplete(item.name)"
                @keydown.enter.prevent="() => null /* disable send form on choose */"
                @keyup.enter.prevent="chooseAutocomplete(item.name)"
                @keyup.esc="domInput().focus(); autocompleteItems = []"
                @keydown.up.prevent="navigation"
                @keydown.down.prevent="navigation"
            >{{ item.name }}</a>
        </div>
    </div>
</template>
<script lang="ts">
import { defineComponent } from 'vue'
import ElTextArea from './text-area.vue'
import ElTextInput from './text-input.vue'
import Icona from '@/components/icona.vue'
import { Item } from '@/types.ts'

export default defineComponent({
    components: { Icona, ElTextArea, ElTextInput },
    inheritAttrs: false,
    props: {
        id: {
            type: String,
            required: true,
        },
        enabled: {
            type: Boolean,
            default: true,
        },
        value: {
            type: [String, Number, Array],
            default: '',
        },
        liveSearchUrl: {
            type: String,
            required: true,
        },
        inputType: {
            type: String,
            default: 'text-input',
        },
        inputClass: {
            type: String,
            default: null,
        },
        isInvalid: {
            type: Boolean,
            default: false,
        },
    },
    emits: ['change', 'submit', 'input'],
    data () {
        return {
            inputRow: 0,
            inputLines: [] as string[],
            focused: {
                input: false,
                autocomplete: false,
            },
            autocompleteVisible: false,
            autocompleteItems: [] as Item[],
            liveSearchTimeout: 0,
            loading: false,
            awaiting: false,
        }
    },
    computed: {
        loadingIconName () {
            return (this.loading && 'icn-spinner') ||
                (this.awaiting && 'icn-spinner') ||
                (this.autocompleteItems.length && 'icn-caret-down') ||
                'icn-no-icon'
        },
        loadingAnimationClass () {
            return (this.loading && 'icona-spinner') ||
                (this.awaiting && 'text-body-secondary') ||
                (this.autocompleteItems.length && 'text-body-secondary') ||
                'display-none'
        },
        isMultiline () {
            return this.inputType === 'text-area'
        },
        inputProps () {
            return {
                id: this.id,
                enabled: this.enabled,
            }
        },
    },
    methods: {
        inputText (value: string, event: KeyboardEvent) {
            this.$emit('input', value, event)
            if (this.isMultiline) {
                const input: HTMLTextAreaElement = event.target as HTMLTextAreaElement
                this.inputLines = input.value.split('\n')
                let cursor = 0
                for (this.inputRow = 0; this.inputRow < this.inputLines.length; this.inputRow++) {
                    cursor += this.inputLines[this.inputRow].length
                    if (cursor >= input.selectionStart) {
                        break
                    }
                    cursor++ // line break
                }
                value = this.inputLines[this.inputRow]
            }

            this.autocompleteValue(value)
        },
        autocompleteValue (value: string) {
            clearTimeout(this.liveSearchTimeout)
            this.loading = false
            if (value.length === 0) {
                this.awaiting = false
                this.autocompleteItems = []
                return
            }
            this.awaiting = true
            this.liveSearchTimeout = setTimeout(() => {
                this.awaiting = false
                this.loading = true
                this.$fetch(this.liveSearchUrl, { q: value })
                    .then((data: Item[]) => {
                        this.autocompleteVisible = true
                        this.autocompleteItems = this.filterExists(data)
                    })
                    .finally(() => this.loading = false)
            }, 500) as unknown as number
        },
        chooseAutocomplete (value: string) {
            if (this.isMultiline) {
                const prev: string = this.inputLines.splice(this.inputRow, 1, value)[0]
                this.inputRow++
                this.autocompleteItems = this.filterExists(this.autocompleteItems)
                if (this.autocompleteItems.length) {
                    this.inputLines.splice(this.inputRow, 0, prev)
                } else {
                    this.domInput().focus()
                }

                const cursor: number = this.inputLines.slice(0, this.inputRow + 1).join('\n').length
                value = this.inputLines.join('\n')
                this.$nextTick(() => this.domInput().setSelectionRange(cursor, cursor))
            } else {
                this.autocompleteVisible = false
                this.domInput().focus()
            }

            this.$emit('change', value)
        },
        focus (item: string, value: boolean) {
            this.focused[item as 'input' | 'autocomplete'] = value
            setTimeout(() => this.autocompleteVisible = this.focused.input || this.focused.autocomplete, 100)
        },
        navigation (event: KeyboardEvent) {
            if (this.autocompleteVisible) {
                const autocomplete: HTMLDivElement = this.$refs.autocomplete as HTMLDivElement
                const items = [this.domInput(), ...((autocomplete?.children as HTMLCollection || []))] as HTMLElement[]
                const focusOffset = { ArrowDown: 1, ArrowUp: -1 }[event.key]
                const target = focusOffset ? items.indexOf(event.target as HTMLElement) + focusOffset : undefined
                if (target && items[target]) {
                    items[target].focus()
                }
            }
            return true
        },
        domInput (): HTMLTextAreaElement | HTMLInputElement {
            return (this.$refs.input as {$el: HTMLElement}).$el as HTMLTextAreaElement | HTMLInputElement
        },
        filterExists (values: Item[]): Item[] {
            return values.filter(item => !this.inputLines.includes(item.name))
        },
    },
})
</script>
