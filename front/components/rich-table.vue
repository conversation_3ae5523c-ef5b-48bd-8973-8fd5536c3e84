<template>
    <div>
        <div
            v-if="showTitle"
            class="d-flex"
        >
            <h4 class="align-content-start mb-2 me-2">
                {{ title === undefined ? $route.meta.title : title }}
                <template v-if="showTotal">
                    ({{ table?.total }})
                </template>
            </h4>

            <h4 class="flex-grow-1 me-2">
                <button
                    v-if="showRefresh"
                    type="button"
                    class="btn btn-sm btn-secondary me-2"
                    :disabled="disabled"
                    @click.prevent="refresh"
                >
                    <Icona
                        name="icn-refresh"
                        :class="{'icona-spinner': disabled}"
                    />
                </button>
                <button
                    v-if="showReset"
                    class="btn btn-sm btn-secondary me-2"
                    @click.prevent="formReset"
                >
                    <Icona name="icn-reset" /> Reset filters
                </button>

                <slot
                    name="afterTitle"
                    :filters="form?.values || {}"
                    :refreshCallback="refresh"
                    :disabled="disabled"
                />
            </h4>
        </div>

        <FormGrid
            v-bind="form"
            :enabled="!disabled"
            @change="reloadOnChange ? formSubmit($event) : formChanged($event)"
            @submit="formSubmit"
        >
            <template
                v-for="slotName in formSlotNames"
                #[slotName]="scope"
            >
                <slot
                    :name="slotName"
                    v-bind="scope"
                    :refreshCallback="refresh"
                />
            </template>
        </FormGrid>


        <EntityTable
            v-if="table && table.data && table.data.length > 0 || !disabled"
            v-bind="table"
            :class="$attrs.class"
            :disabled="disabled"
            :tableClasses="tableClasses"
            :showRowsCount="showTotal"
            :showHeader="showTableHeader"
            :sticky="sticky"
            @sortChanged="sortChanged"
        >
            <template
                v-for="slotName in rowsSlotNames"
                #[slotName]="scope"
            >
                <slot
                    :name="slotName"
                    v-bind="scope"
                    :refreshCallback="refresh"
                />
            </template>
        </EntityTable>

        <Pagination
            v-show="showPagination"
            v-bind="pagination"
            :disabled="disabled"
            @pageChanged="pageChanged"
        />
    </div>
</template>

<script lang="ts" setup>
import EntityTable from './entity-table/table.vue'
import FormGrid from './form/grid.vue'
import Icona from './icona.vue'
import Pagination, { PageChangedEvent } from './pagination.vue'
import { computed } from 'vue'
import { FormElement, RichTableType, Values } from '@/types.ts'
import { useRoute } from 'vue-router'

defineOptions({
    inheritAttrs: false,
})

const $route = useRoute()

const $props = withDefaults(defineProps<RichTableType>(), {
    title: undefined,
    showTitle: true,
    showTotal: true,
    showTableHeader: true,
    form: undefined,
    table: undefined,
    tableClasses: undefined,
    pagination: undefined,
    reloadOnChange: true,
})

const $emit = defineEmits<{
    change: [values: Values]
    reload: [values: Values]
}>()

const rowsSlotNames = computed((): string[] => {
    if (!$props.table?.columns) {
        return []
    }

    return $props.table.columns
        .map(({slotName}) => slotName)
        .filter(slotName => slotName) as string[]
})

const formSlotNames = computed((): string[] => {
    return ($props.form?.blocks ?? [])
        .flatMap((row: FormElement[]) => row)
        .map((element: FormElement) => element.slotName)
        .filter(Boolean) as string[]
})

function formChanged(newValues: Values) {
    const values = Object.assign({}, newValues)
    $emit('change', values)
}

function formSubmit(newValues: Values) {
    const values = Object.assign({}, newValues)
    delete values.page
    reload(values, true, false)
}

function formReset() {
    reload({}, false, false)
}

function sortChanged(newSort: string) {
    const values = Object.assign({}, $props.form?.values || {}, {
        sort: newSort,
    })

    reload(values, false, true)
}

function pageChanged({ page }: PageChangedEvent) {
    const values = Object.assign({}, $props.form?.values || {}, {
        page,
    })
    reload(values, true, false)
}

function refresh() {
    const values = Object.assign({}, $props.form?.values || {})
    reload(values, true, true)
}

function reload(values: Values, addSort: boolean, addPage: boolean) {
    if (addSort && $props.table?.sort) {
        values.sort = $props.table.sort
    }

    if (addPage && $props.pagination && $props.pagination.page !== 1) {
        values.page = $props.pagination.page as number
    }

    $emit('reload', values)
}
</script>;
