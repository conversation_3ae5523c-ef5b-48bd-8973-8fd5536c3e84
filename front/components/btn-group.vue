<template>
    <div
        class="input-group"
        :class="'input-group-' + size"
        v-bind="$attrs"
    >
        <label v-if="prefix" class="input-group-text">{{ prefix }}</label>
        <button
            v-for="item in items"
            :key="item.id"
            :disabled="isDisabled(item.id)"
            v-bind="itemForRender(item)"
            @click="toggle(item.id)"
        >
            <Icona
                v-if="item.icon"
                :name="item.icon"
            />
            {{ displayText ? item.name : '' }}
        </button>
        <template v-if="multiple && items.length > 1">
            <button
                class="btn btn-secondary"
                :disabled="disabled"
                @click="selectAll"
            >
                All
            </button>
            <button
                class="btn btn-secondary"
                :disabled="disabled"
                @click="deselectAll"
            >
                None
            </button>
        </template>
    </div>
</template>

<script lang="ts" setup generic="Item extends BtnGroupItem = BtnGroupItem">
import { BtnGroupItem, Id, BsSize, ClassList } from '@/types'
import Icona from './icona.vue'
import { computed } from 'vue'

const $props = withDefaults(defineProps<{
    items?: Item[]
    value?: Item['id'][]
    multiple?: boolean
    size?: BsSize
    displayText?: boolean
    disabled?: boolean
    disableActive?: boolean
    prefix?: string
}>(), {
    items: undefined,
    value: undefined,
    multiple: true,
    size: 'md',
    displayText: true,
    prefix: undefined,
})

const $emit = defineEmits<{
    change: [newValue: Item['id'][]]
}>()

const valueOrEmpty = computed(()  => $props.value || [])

const toggle = (value: number|string) => {
    const idx = valueOrEmpty.value.indexOf(value)
    let newValue = [...valueOrEmpty.value]

    if (idx === -1 && $props.multiple) {
        newValue.push(value)
    } else if (idx === -1) {
        newValue = [value]
    } else {
        newValue.splice(idx, 1)
    }

    emitAction(newValue)
}

const deselectAll = () => {
    emitAction([])
}

const selectAll = () => {
    emitAction(($props.items || []).map((it: BtnGroupItem) => it.id))
}

const itemForRender = (i: BtnGroupItem) => {
    const item = {
        title: i.title ? i.title : i.name,
        class: {
            btn: true,
        } as ClassList,
    }

    if (i.class instanceof String) {
        item.class = Object.assign(item.class, Object.fromEntries(i.class.split(' ').map(c => [c, true])))
    }

    if (valueOrEmpty.value.indexOf(i.id) !== -1) {
        item.class.active = true
        const activeClass = i.activeClass ? i.activeClass : 'btn-primary'
        item.class[activeClass] = true
    } else {
        item.class['btn-outline-secondary'] = true
    }

    return item
}

const emitAction = (newValue: Id[]) => {
    $emit('change', newValue)
}

const isDisabled = (id: Id): boolean => {
    return $props.disabled || ($props.disableActive && valueOrEmpty.value.indexOf(id) !== -1)
}
</script>
