import { UserAuthConfig } from '@/types.ts'
import { useFetch } from '@/utils/fetch.ts'
import { computed, ref } from 'vue'

const $fetch = useFetch()

const userAuthConfig = ref(null as UserAuthConfig | null)

const loggedUser = computed(() => {
    if (userAuthConfig.value !== null) {
        return userAuthConfig.value
    }

    const storageUser = localStorage.getItem('user')

    if (storageUser === null) {
        return null
    }

    userAuthConfig.value = JSON.parse(storageUser)

    return userAuthConfig.value
})

const isGuest = computed(() => {
    return loggedUser.value === null
})

export function useAuthUser () {
    function saveUser (data: UserAuthConfig): void {
        localStorage.setItem('user', JSON.stringify(data))
        userAuthConfig.value = data
    }

    async function reloadConfig () {
        const data: UserAuthConfig = await $fetch('/auth/auth/config')
        saveUser(data)

        return data
    }

    function can (permission: keyof UserAuthConfig['can']) {
        if (loggedUser.value === null) {
            return false
        }

        return loggedUser.value.can[permission]
    }

    function logout () {
        $fetch('/auth/auth/sign-out').finally(needLogin)
    }

    function needLogin () {
        clearStorage()
    }

    function clearStorage () {
        localStorage.removeItem('user')
        userAuthConfig.value = null
    }

    async function hasFeedbackFlagReset () {
        await $fetch('/auth/auth/has-feedback-flag-reset')
    }

    function resetEmployeeRequestHasFeedback () {
        userAuthConfig.value!.lastRequest.hasFeedback = false
    }

    return {
        loggedUser,
        isGuest,
        saveUser,
        reloadConfig,
        can,
        logout,
        needLogin,
        clearStorage,
        hasFeedbackFlagReset,
        resetEmployeeRequestHasFeedback,
    }
}

