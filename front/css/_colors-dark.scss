@use "sass:map";
@use "sass:color";
@import "colors";

$white-dark: #BBB;
$gray-100-dark: #AAA;
$gray-200-dark: #999;
$gray-300-dark: #888;
$gray-400-dark: #777;
$gray-500-dark: #666;
$gray-600-dark: #555;
$gray-700-dark: #444;
$gray-800-dark: #333;
$gray-900-dark: #222;
$black-dark: #000;
$cyan-muted: #055160;

//necessary for further use in scss-files
$primary-dark: shift-color($fu-belize-hole, 22%);
$success-dark: shift-color($fu-nephritis, 40%);
$info-dark: shift-color($fu-turquoise, 20%);
$warning-dark: shift-color($fu-sun-flower, 20%);
$danger-dark: shift-color($fu-pumpkin, 15%);

$dark-theme-colors: map.merge((
    "primary":    $primary-dark,
    "secondary":  $gray-600-dark,
    "success":    $success-dark,
    "info":       $info-dark,
    "warning":    $warning-dark,
    "danger":     $danger-dark,
    "dark":       $gray-800-dark,

    "light-blue": shift-color($fu-belize-hole, -30%),
    "blue":       shift-color($fu-peter-river, 20%),
    "indigo":     shift-color($fu-indigo, 20%),
    "purple":     shift-color($fu-wisteria, 20%),
    "pink":       shift-color($fu-plum, 20%),
    "red":        shift-color($fu-pomegranate, 20%),
    "orange":     shift-color($fu-carrot, 20%),
    "yellow":     shift-color($fu-sun-flower, 20%),
    "green":      shift-color($fu-nephritis, 20%),
    "teal":       shift-color($fu-green-sea, 20%),
    "cyan":       shift-color($fu-turquoise, 20%),
    "gray":       shift-color($fu-asbestos, 15%),
    "cyan-muted": $cyan-muted,
), $fu-colors);
$theme-colors-text-dark: $dark-theme-colors;

// Body
$body-color-dark:                   $white-dark;
$body-bg-dark:                      $gray-800-dark;
$body-emphasis-color-dark:          $body-color-dark;
$body-secondary-color-dark:         $gray-300-dark; // old text-muted
$body-secondary-bg-dark:            $gray-600-dark;
$body-tertiary-color-dark:          $body-color-dark;
$body-tertiary-bg-dark:             $gray-700-dark;

// Links
$link-color-dark:                   color.adjust($primary-dark, $lightness: 10%);

// Borders
$border-color-dark:                 $gray-600-dark;
$border-color-translucent-dark:     rgba($body-color-dark, .1);

$form-invalid-color-dark:           $danger-dark;

@include color-mode(dark, true) {
    $theme-colors: $dark-theme-colors;

    // Helper classes for theme colors. like in _root.scss for light colors
    @each $color-name, $color in $theme-colors {
        --bs-#{$color-name}: #{$color};
        --bs-#{$color-name}-rgb: #{to-rgb($color)};
    }

    body {
        // Override body color to minimize other components background color change
        background-color: #{$gray-900-dark};
    }

    .navbar {
        background-color: #{$body-bg-dark};
        --bs-navbar-color: #{$gray-100-dark};
    }

    .dropdown-menu {
        --bs-dropdown-bg: #{$body-bg-dark};
        --bs-dropdown-header-color: #{$body-secondary-color-dark}
    }

    .popover {
        --bs-popover-header-bg: #{$body-tertiary-bg-dark};
    }

    .progress {
        --bs-progress-bg: #{$body-tertiary-bg-dark};
    }

    // Style for override theme colored buttons. We need to tint all theme colors
    $color-contrast-dark:             $black-dark;
    $color-contrast-light:            $white-dark;
    $btn-hover-bg-shade-amount:       15%;
    $btn-hover-bg-tint-amount:        15%;
    $btn-hover-border-shade-amount:   20%;
    $btn-hover-border-tint-amount:    10%;
    $btn-active-bg-shade-amount:      20%;
    $btn-active-bg-tint-amount:       20%;
    $btn-active-border-shade-amount:  25%;
    $btn-active-border-tint-amount:   10%;


    // Buttons
    @each $btn-name, $background in $theme-colors {
        $border: $background;
        $color: color-contrast($background, $color-contrast-dark, $color-contrast-light);
        $hover-background: if($color == $color-contrast-light, shade-color($background, $btn-hover-bg-shade-amount), tint-color($background, $btn-hover-bg-tint-amount));
        $hover-border: if($color == $color-contrast-light, shade-color($border, $btn-hover-border-shade-amount), tint-color($border, $btn-hover-border-tint-amount));
        $hover-color: color-contrast($hover-background, $color-contrast-dark, $color-contrast-light);
        $active-background: if($color == $color-contrast-light, shade-color($background, $btn-active-bg-shade-amount), tint-color($background, $btn-active-bg-tint-amount));
        $active-border: if($color == $color-contrast-light, shade-color($border, $btn-active-border-shade-amount), tint-color($border, $btn-active-border-tint-amount));
        $active-color: color-contrast($active-background, $color-contrast-dark, $color-contrast-light);
        $disabled-background: $background;
        $disabled-border: $border;
        $disabled-color: color-contrast($disabled-background, $color-contrast-dark, $color-contrast-light);

        .btn-#{$btn-name} {
            // variables names copied form @mixin button-variant, all without shadows
            --bs-btn-color: #{$color};
            --bs-btn-bg: #{$background};
            --bs-btn-border-color: #{$border};
            --bs-btn-hover-color: #{$hover-color};
            --bs-btn-hover-bg: #{$hover-background};
            --bs-btn-hover-border-color: #{$hover-border};
            --bs-btn-focus-shadow-rgb: #{to-rgb(mix($color, $border, 15%))};
            --bs-btn-active-color: #{$active-color};
            --bs-btn-active-bg: #{$active-background};
            --bs-btn-active-border-color: #{$active-border};
            --bs-btn-disabled-color: #{$disabled-color};
            --bs-btn-disabled-bg: #{$disabled-background};
            --bs-btn-disabled-border-color: #{$disabled-border};
        }
    }

    // Buttons outline
    @each $btn-name, $color in $theme-colors {
        $color-hover: color-contrast($color, $color-contrast-dark, $color-contrast-light);
        $active-background: $color;
        $active-border: $color;
        $active-color: color-contrast($active-background, $color-contrast-dark, $color-contrast-light);

        .btn-outline-#{$btn-name} {
            // variables names copied form @mixin button-outline-variant, all without shadows
            --bs-btn-color: #{$color};
            --bs-btn-border-color: #{$color};
            --bs-btn-hover-color: #{$color-hover};
            --bs-btn-hover-bg: #{$active-background};
            --bs-btn-hover-border-color: #{$active-border};
            --bs-btn-focus-shadow-rgb: #{to-rgb($color)};
            --bs-btn-active-color: #{$active-color};
            --bs-btn-active-bg: #{$active-background};
            --bs-btn-active-border-color: #{$active-border};
            --bs-btn-disabled-color: #{$color};
            --bs-btn-disabled-bg: transparent;
            --bs-btn-disabled-border-color: #{$color};
            --bs-gradient: none;
        }
    }

    // Override for strong contrast
    .btn-outline-secondary {
        --bs-btn-color: #{$gray-300-dark};
    }

    .badge {
        // Same problem as with buttons above
        @each $bg-name, $color in $theme-colors {
            --bs-#{$bg-name}: $color;
            --bs-#{$bg-name}-rgb: #{to-rgb($color)};
        }

        // Override for strong contrast
        &.bg-secondary {
            background-color: var(--bs-gray);
        }

        // Override for strong contrast
        &.bg-warning {
            color: var(--bs-black);
        }
    }
}
