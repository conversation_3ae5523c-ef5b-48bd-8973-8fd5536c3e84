@use "sass:map";
@import "colors";
@import "bootstrap/scss/functions";

/* Bootstrap colors */
$white: #FFF;
$gray-100: #f8f9fa;
$gray-200: #e9ecef;
$gray-300: #dee2e6;
$gray-400: #ced4da;
$gray-500: #adb5bd;
$gray-600: #6c757d;
$gray-700: #495057;
$gray-800: #343a40;
$gray-900: $fu-midnight-blue;
$black: #000;
$cyan-muted: #cff4fc;

//necessary for further use in scss-files
$primary: shift-color($fu-belize-hole, 5%);
$success: shift-color($fu-nephritis, 25%);
$info: $fu-turquoise;
$warning: $fu-sun-flower;
$danger: $fu-pumpkin;

$theme-colors: map.merge((
    "primary": $primary,
    "secondary": $gray-400,
    "success": $success,
    "info": $info,
    "warning": $warning,
    "danger": $danger,
    "dark": $gray-900,

    "light-blue": shift-color($fu-belize-hole, -40%),
    "blue": shift-color($fu-peter-river, -10%),
    "indigo": $fu-indigo,
    "purple": $fu-wisteria,
    "pink": $fu-plum,
    "red": $fu-pomegranate,
    "orange": $fu-carrot,
    "yellow": $fu-sun-flower,
    "green": $fu-nephritis,
    "teal": $fu-green-sea,
    "cyan": $fu-turquoise,
    "gray": shift-color($fu-asbestos, -30%),
    "cyan-muted": $cyan-muted,
), $fu-colors);

$body-bg: $gray-100;
$body-color: $gray-900;
$body-emphasis-color: $body-color;
$body-tertiary-bg: $gray-200;

$fu-sun-flower-darker: shift-color($fu-sun-flower, 20%); // Because if function shift-color doesn't work in @include color-mode(light,...

@include color-mode(light, true) {
    .btn-outline-secondary {
        --bs-btn-color: #{$body-color};
    }
    .btn-outline-warning {
        --bs-btn-color: #{$fu-sun-flower-darker};
        --bs-btn-border-color: #{$fu-sun-flower-darker};
    }

    .navbar {
        background-color: #{$primary};
        --bs-navbar-color: #{$gray-300};
        --bs-navbar-hover-color: #{$white};
    }
}
